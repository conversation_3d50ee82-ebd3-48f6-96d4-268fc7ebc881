# PCAP下载相关配置
app:
  pcap:
    # PCAP文件存储配置
    storage:
      base-path: /data  # PCAP文件存储根目录
    
    # 下载任务配置
    download:
      temp-path: /tmp/pcap-download  # 临时文件目录
      max-file-size: **********  # 单个文件最大大小 (1GB)
      max-session-count: 1000  # 单次下载最大会话数量
      
    # mergecap工具配置
    mergecap:
      command: /usr/bin/mergecap  # mergecap命令路径
      timeout: 300  # 执行超时时间（秒）
      
  # 任务配置
  task:
    pcap:
      expire-hours: 24  # 下载文件过期时间（小时）
      max-file-size: **********  # 最大文件大小

# 异步任务执行器配置
spring:
  task:
    execution:
      pool:
        core-size: 4
        max-size: 16
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: pcap-task-
      
# 事件处理配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  metrics:
    export:
      prometheus:
        enabled: true
