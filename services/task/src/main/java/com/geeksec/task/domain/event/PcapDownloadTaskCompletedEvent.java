package com.geeksec.task.domain.event;

import lombok.Getter;

/**
 * PCAP下载任务完成事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public class PcapDownloadTaskCompletedEvent extends PcapDownloadEvent {

    /**
     * 下载文件路径
     */
    private final String downloadUrl;

    /**
     * 文件大小（字节）
     */
    private final Long fileSize;

    /**
     * 处理耗时（毫秒）
     */
    private final Long processingTimeMs;

    /**
     * 合并的文件数量
     */
    private final Integer mergedFileCount;

    public PcapDownloadTaskCompletedEvent(Object source, Integer taskId, String userId, 
                                        String downloadUrl, Long fileSize, 
                                        Long processingTimeMs, Integer mergedFileCount) {
        super(source, taskId, userId, "PCAP下载任务已完成");
        this.downloadUrl = downloadUrl;
        this.fileSize = fileSize;
        this.processingTimeMs = processingTimeMs;
        this.mergedFileCount = mergedFileCount;
    }
}
