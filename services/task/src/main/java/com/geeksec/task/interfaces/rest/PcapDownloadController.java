package com.geeksec.task.interfaces.rest;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.task.application.service.PcapDownloadService;
import com.geeksec.task.application.service.PcapDownloadService.PcapDownloadRequest;
import com.geeksec.task.application.service.PcapDownloadService.PcapDownloadTaskResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * PCAP下载任务控制器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/pcap-download")
@RequiredArgsConstructor
@Validated
@Tag(name = "PCAP下载任务", description = "PCAP文件下载任务管理")
public class PcapDownloadController {

    private final PcapDownloadService pcapDownloadService;

    /**
     * 创建PCAP下载任务
     */
    @PostMapping("/create")
    @Operation(summary = "创建下载任务", description = "创建PCAP文件下载任务")
    public ApiResponse<PcapDownloadTaskResult> createPcapDownloadTask(
            @Parameter(description = "下载请求", required = true)
            @Valid @RequestBody PcapDownloadRequest request) {
        try {
            log.info("创建PCAP下载任务: userId={}, sessionCount={}", 
                    request.userId(), request.sessionIds().size());

            PcapDownloadTaskResult result = pcapDownloadService.createPcapDownloadTask(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("创建PCAP下载任务失败", e);
            return ApiResponse.error("创建下载任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询下载任务状态
     */
    @GetMapping("/{taskId}/status")
    @Operation(summary = "查询任务状态", description = "查询PCAP下载任务的状态和进度")
    public ApiResponse<PcapDownloadTaskResult> getDownloadTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId) {
        try {
            PcapDownloadTaskResult result = pcapDownloadService.getDownloadTaskStatus(taskId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询下载任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消下载任务
     */
    @PostMapping("/{taskId}/cancel")
    @Operation(summary = "取消下载任务", description = "取消正在执行的下载任务")
    public ApiResponse<Boolean> cancelDownloadTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "用户ID", required = true)
            @RequestParam String userId) {
        try {
            boolean success = pcapDownloadService.cancelDownloadTask(taskId, userId);
            return ApiResponse.success(success);
        } catch (Exception e) {
            log.error("取消下载任务失败: taskId={}, userId={}", taskId, userId, e);
            return ApiResponse.error("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 删除下载任务
     */
    @DeleteMapping("/{taskId}")
    @Operation(summary = "删除下载任务", description = "删除下载任务和相关文件")
    public ApiResponse<Boolean> deleteDownloadTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "用户ID", required = true)
            @RequestParam String userId) {
        try {
            boolean success = pcapDownloadService.deleteDownloadTask(taskId, userId);
            return ApiResponse.success(success);
        } catch (Exception e) {
            log.error("删除下载任务失败: taskId={}, userId={}", taskId, userId, e);
            return ApiResponse.error("删除任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的下载任务列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "查询用户任务", description = "查询用户的下载任务列表")
    public ApiResponse<List<PcapDownloadTaskResult>> getUserDownloadTasks(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小", example = "10")
            @RequestParam(defaultValue = "10") int size) {
        try {
            List<PcapDownloadTaskResult> tasks = pcapDownloadService.getUserDownloadTasks(userId, page, size);
            return ApiResponse.success(tasks);
        } catch (Exception e) {
            log.error("查询用户下载任务失败: userId={}", userId, e);
            return ApiResponse.error("查询用户任务失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期任务
     */
    @PostMapping("/cleanup")
    @Operation(summary = "清理过期任务", description = "清理过期的下载任务和文件")
    public ApiResponse<Integer> cleanupExpiredTasks() {
        try {
            int cleanedCount = pcapDownloadService.cleanupExpiredTasks();
            return ApiResponse.success(cleanedCount);
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
            return ApiResponse.error("清理过期任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取任务统计", description = "获取下载任务的统计信息")
    public ApiResponse<TaskStatistics> getTaskStatistics() {
        try {
            // TODO: 实现任务统计逻辑
            TaskStatistics statistics = new TaskStatistics(0, 0, 0, 0);
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            log.error("获取任务统计失败", e);
            return ApiResponse.error("获取任务统计失败: " + e.getMessage());
        }
    }

    /**
     * 任务统计信息
     */
    public record TaskStatistics(
        long totalTasks,
        long pendingTasks,
        long processingTasks,
        long completedTasks
    ) {}
}
