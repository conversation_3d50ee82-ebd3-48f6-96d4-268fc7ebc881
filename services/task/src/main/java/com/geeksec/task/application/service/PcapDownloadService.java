package com.geeksec.task.application.service;

import com.geeksec.task.model.entity.PcapDownloadTask;

import java.util.List;

/**
 * PCAP下载服务接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface PcapDownloadService {

    /**
     * 创建PCAP下载任务
     * 
     * @param request 下载请求
     * @return 任务信息
     */
    PcapDownloadTaskResult createPcapDownloadTask(PcapDownloadRequest request);

    /**
     * 查询下载任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    PcapDownloadTaskResult getDownloadTaskStatus(Integer taskId);

    /**
     * 处理PCAP下载任务
     * 
     * @param task 下载任务
     * @return 处理结果
     */
    boolean processPcapDownloadTask(PcapDownloadTask task);

    /**
     * 取消下载任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean cancelDownloadTask(Integer taskId, String userId);

    /**
     * 删除下载任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteDownloadTask(Integer taskId, String userId);

    /**
     * 获取用户的下载任务列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页大小
     * @return 任务列表
     */
    List<PcapDownloadTaskResult> getUserDownloadTasks(String userId, int page, int size);

    /**
     * 清理过期的下载任务
     * 
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks();

    /**
     * PCAP下载请求
     */
    record PcapDownloadRequest(
        String userId,
        String alarmType,
        Long alarmTime,
        List<String> sessionIds,
        String description
    ) {}

    /**
     * PCAP下载任务结果
     */
    record PcapDownloadTaskResult(
        Integer taskId,
        String userId,
        String alarmType,
        Long alarmTime,
        List<String> sessionIds,
        String status,
        Integer progress,
        Integer totalFileCount,
        Integer processedFileCount,
        Long archiveFileSize,
        String downloadUrl,
        String errorMessage,
        java.time.LocalDateTime createTime,
        java.time.LocalDateTime startTime,
        java.time.LocalDateTime completeTime,
        java.time.LocalDateTime expireTime
    ) {}

    /**
     * 文件信息
     */
    record FileInfo(
        String filePath,
        Long fileSize,
        boolean exists
    ) {}

    /**
     * 下载进度信息
     */
    record DownloadProgress(
        Integer taskId,
        String status,
        Integer progress,
        Integer totalFiles,
        Integer processedFiles,
        String currentFile,
        String message
    ) {}
}
