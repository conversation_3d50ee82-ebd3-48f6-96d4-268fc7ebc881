package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 下载任务注册表定义类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 * @note 此类由 MyBatis-Flex 注解处理器自动生成
 */
public class DownloadTaskRegisterTableDef extends TableDef {

    /**
     * 下载任务注册表定义
     */
    public static final DownloadTaskRegisterTableDef DOWNLOAD_TASK_REGISTER = new DownloadTaskRegisterTableDef();

    /**
     * 主键
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 文件路径
     */
    public final QueryColumn PATH = new QueryColumn(this, "path");

    /**
     * 查询条件
     */
    public final QueryColumn QUERY = new QueryColumn(this, "query");

    /**
     * 任务类型 (1: 数据准备任务)
     */
    public final QueryColumn TYPE = new QueryColumn(this, "type");

    /**
     * 下载次数
     */
    public final QueryColumn DOWNLOAD_COUNT = new QueryColumn(this, "download_count");

    /**
     * 删除时间
     */
    public final QueryColumn DELETE_TIME = new QueryColumn(this, "delete_time");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 创建时间
     */
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 任务类型
     */
    public final QueryColumn TASK_TYPE = new QueryColumn(this, "task_type");

    /**
     * 错误信息
     */
    public final QueryColumn ERROR_MSG = new QueryColumn(this, "error_msg");

    /**
     * 任务状态 (1: 待处理, 2: 处理中, 3: 已完成, -1: 失败)
     */
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 前端展示查询条件
     */
    public final QueryColumn SHOW_QUERY = new QueryColumn(this, "show_query");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认构造函数
     */
    public DownloadTaskRegisterTableDef() {
        super("tb_download_task_register");
    }

    /**
     * 带别名的构造函数
     */
    public DownloadTaskRegisterTableDef(String alias) {
        super("tb_download_task_register", alias);
    }

}