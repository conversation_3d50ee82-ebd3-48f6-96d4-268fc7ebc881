package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 下载任务表定义类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 * @note 此类由 MyBatis-Flex 注解处理器自动生成
 */
public class DownloadTaskTableDef extends TableDef {

    /**
     * 下载任务表定义
     */
    public static final DownloadTaskTableDef DOWNLOAD_TASK = new DownloadTaskTableDef();

    /**
     * 主键
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 创建者用户ID
     */
    public final QueryColumn CREATED_BY = new QueryColumn(this, "created_by");

    /**
     * 文件路径
     */
    public final QueryColumn PATH = new QueryColumn(this, "path");

    /**
     * ES 下载检索条件
     */
    public final QueryColumn QUERY = new QueryColumn(this, "query");

    /**
     * 前端展示字段
     */
    public final QueryColumn SHOW_QUERY = new QueryColumn(this, "show_query");

    /**
     * 下载类型 (0: 部分下载, 1: 全量下载)
     */
    public final QueryColumn TYPE = new QueryColumn(this, "type");

    /**
     * 会话列表信息
     */
    public final QueryColumn SESSION_ID = new QueryColumn(this, "session_id");

    /**
     * 任务状态 (0: 准备数据, 1: 可下载, 2: 重新下载, 3: 已删除, 4: 待删除)
     */
    public final QueryColumn STATE = new QueryColumn(this, "state");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 数据存储结束时间
     */
    public final QueryColumn END_TIME = new QueryColumn(this, "end_time");

    /**
     * 数据状态 (0: 删除, 1: 存在)
     */
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 关联的任务ID数组
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 下载次数
     */
    public final QueryColumn DOWNLOAD_COUNT = new QueryColumn(this, "download_count");

    /**
     * 删除时间
     */
    public final QueryColumn DELETE_TIME = new QueryColumn(this, "delete_time");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 创建时间（标准格式）
     */
    public final QueryColumn CREATED_AT_STANDARD = new QueryColumn(this, "created_at");

    /**
     * 任务类型
     */
    public final QueryColumn TASK_TYPE = new QueryColumn(this, "task_type");

    /**
     * 错误信息
     */
    public final QueryColumn ERROR_MSG = new QueryColumn(this, "error_msg");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认构造函数
     */
    public DownloadTaskTableDef() {
        super("tb_download_task");
    }

    /**
     * 带别名的构造函数
     */
    public DownloadTaskTableDef(String alias) {
        super("tb_download_task", alias);
    }

}