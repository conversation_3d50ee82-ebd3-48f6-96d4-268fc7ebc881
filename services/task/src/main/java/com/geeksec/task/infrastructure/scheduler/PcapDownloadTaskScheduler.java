package com.geeksec.task.infrastructure.scheduler;

import com.geeksec.task.infrastructure.processor.PcapDownloadTaskProcessor;
import com.geeksec.task.infrastructure.service.FileUploadServiceImpl;
import com.geeksec.task.model.entity.PcapDownloadTask;
import com.geeksec.task.repository.PcapDownloadTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP下载任务调度器
 * 负责定时处理待处理的下载任务和清理过期任务
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.task.pcap.scheduler.enabled", havingValue = "true", matchIfMissing = true)
public class PcapDownloadTaskScheduler {

    private final PcapDownloadTaskRepository pcapDownloadTaskRepository;
    private final PcapDownloadTaskProcessor pcapDownloadTaskProcessor;
    private final FileUploadServiceImpl fileUploadService;

    /**
     * 处理待处理的下载任务
     * 每30秒执行一次
     */
    @Scheduled(fixedDelay = 30000)
    public void processPendingTasks() {
        try {
            List<PcapDownloadTask> pendingTasks = pcapDownloadTaskRepository.getPendingTasks();
            
            if (pendingTasks.isEmpty()) {
                return;
            }

            log.info("发现待处理的PCAP下载任务: 数量={}", pendingTasks.size());

            for (PcapDownloadTask task : pendingTasks) {
                try {
                    log.info("开始处理PCAP下载任务: taskId={}", task.getId());
                    pcapDownloadTaskProcessor.processDownloadTask(task);
                } catch (Exception e) {
                    log.error("处理PCAP下载任务失败: taskId={}", task.getId(), e);
                    
                    // 更新任务状态为失败
                    updateTaskToFailed(task.getId(), e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("处理待处理任务失败", e);
        }
    }

    /**
     * 清理过期的下载任务
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanupExpiredTasks() {
        try {
            log.info("开始清理过期的PCAP下载任务");

            LocalDateTime expireTime = LocalDateTime.now().minusHours(24); // 24小时过期
            List<PcapDownloadTask> expiredTasks = pcapDownloadTaskRepository.getExpiredTasks(expireTime);

            if (expiredTasks.isEmpty()) {
                log.info("没有发现过期的下载任务");
                return;
            }

            log.info("发现过期的下载任务: 数量={}", expiredTasks.size());

            int cleanedCount = 0;
            for (PcapDownloadTask task : expiredTasks) {
                try {
                    // 删除相关文件
                    if (task.getDownloadUrl() != null) {
                        fileUploadService.deleteFile(task.getDownloadUrl());
                    }

                    // 删除任务记录
                    pcapDownloadTaskRepository.removeById(task.getId());
                    cleanedCount++;

                    log.debug("清理过期任务: taskId={}", task.getId());

                } catch (Exception e) {
                    log.error("清理过期任务失败: taskId={}", task.getId(), e);
                }
            }

            log.info("清理过期任务完成: 清理数量={}", cleanedCount);

        } catch (Exception e) {
            log.error("清理过期任务失败", e);
        }
    }

    /**
     * 清理过期文件
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredFiles() {
        try {
            log.info("开始清理过期文件");

            LocalDateTime expireTime = LocalDateTime.now().minusDays(7); // 7天过期
            int cleanedCount = fileUploadService.cleanupExpiredFiles(expireTime);

            log.info("清理过期文件完成: 清理数量={}", cleanedCount);

        } catch (Exception e) {
            log.error("清理过期文件失败", e);
        }
    }

    /**
     * 监控任务状态
     * 每5分钟执行一次
     */
    @Scheduled(fixedDelay = 300000)
    public void monitorTaskStatus() {
        try {
            // 统计各状态的任务数量
            long pendingCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                PcapDownloadTask.TaskStatus.PENDING.getCode());
            long processingCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                PcapDownloadTask.TaskStatus.PROCESSING.getCode());
            long completedCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                PcapDownloadTask.TaskStatus.COMPLETED.getCode());
            long failedCount = pcapDownloadTaskRepository.getTaskCountByStatus(
                PcapDownloadTask.TaskStatus.FAILED.getCode());

            log.info("PCAP下载任务状态统计: 待处理={}, 处理中={}, 已完成={}, 失败={}", 
                    pendingCount, processingCount, completedCount, failedCount);

            // 检查长时间处理中的任务
            checkLongRunningTasks();

        } catch (Exception e) {
            log.error("监控任务状态失败", e);
        }
    }

    /**
     * 检查长时间处理中的任务
     */
    private void checkLongRunningTasks() {
        try {
            // 查找超过2小时还在处理中的任务
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(2);
            
            // TODO: 添加查询长时间处理中任务的方法
            // List<PcapDownloadTask> longRunningTasks = pcapDownloadTaskRepository.getLongRunningTasks(cutoffTime);
            
            // for (PcapDownloadTask task : longRunningTasks) {
            //     log.warn("发现长时间处理中的任务: taskId={}, startTime={}", task.getId(), task.getStartTime());
            //     
            //     // 可以选择将这些任务标记为失败
            //     updateTaskToFailed(task.getId(), "任务处理超时");
            // }

        } catch (Exception e) {
            log.error("检查长时间处理中的任务失败", e);
        }
    }

    /**
     * 更新任务状态为失败
     */
    private void updateTaskToFailed(Integer taskId, String errorMessage) {
        try {
            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(taskId);
            updateTask.setStatus(PcapDownloadTask.TaskStatus.FAILED.getCode());
            updateTask.setErrorMessage(errorMessage);
            updateTask.setUpdateTime(LocalDateTime.now());

            pcapDownloadTaskRepository.updateById(updateTask);
            log.info("任务状态更新为失败: taskId={}, error={}", taskId, errorMessage);

        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", taskId, e);
        }
    }
}
