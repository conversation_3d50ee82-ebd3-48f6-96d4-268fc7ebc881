package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * PCAP下载任务实体类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("pcap_download_task")
public class PcapDownloadTask implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 任务ID（关联tb_task表）
     */
    @Column("task_id")
    private Integer taskId;

    /**
     * 创建者用户ID
     */
    @Column("user_id")
    private String userId;

    /**
     * 告警类型
     */
    @Column("alarm_type")
    private String alarmType;

    /**
     * 告警时间
     */
    @Column("alarm_time")
    private Long alarmTime;

    /**
     * 会话ID列表（JSON格式）
     */
    @Column("session_ids")
    private String sessionIds;

    /**
     * PCAP文件路径列表（JSON格式）
     */
    @Column("pcap_file_paths")
    private String pcapFilePaths;

    /**
     * 压缩包文件路径
     */
    @Column("archive_file_path")
    private String archiveFilePath;

    /**
     * 压缩包文件大小（字节）
     */
    @Column("archive_file_size")
    private Long archiveFileSize;

    /**
     * 总文件数量
     */
    @Column("total_file_count")
    private Integer totalFileCount;

    /**
     * 已处理文件数量
     */
    @Column("processed_file_count")
    private Integer processedFileCount;

    /**
     * 任务状态
     * 1: 待处理, 2: 处理中, 3: 已完成, -1: 失败
     */
    @Column("status")
    private Integer status;

    /**
     * 进度百分比 (0-100)
     */
    @Column("progress")
    private Integer progress;

    /**
     * 错误信息
     */
    @Column("error_message")
    private String errorMessage;

    /**
     * 下载URL
     */
    @Column("download_url")
    private String downloadUrl;

    /**
     * 文件过期时间
     */
    @Column("expire_time")
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 开始处理时间
     */
    @Column("start_time")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @Column("complete_time")
    private LocalDateTime completeTime;

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING(1, "待处理"),
        PROCESSING(2, "处理中"),
        COMPLETED(3, "已完成"),
        FAILED(-1, "失败");

        private final int code;
        private final String description;

        TaskStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TaskStatus fromCode(int code) {
            for (TaskStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status code: " + code);
        }
    }
}
