package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 任务表定义
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 * @generated by MyBatis-Flex
 */
public class TaskTableDef extends TableDef {

    /**
     * 任务表定义
     */
    public static final TaskTableDef TASK = new TaskTableDef();

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 任务名称
     */
    public final QueryColumn TASK_NAME = new QueryColumn(this, "task_name");

    /**
     * 任务描述
     */
    public final QueryColumn TASK_DESCRIPTION = new QueryColumn(this, "task_description");

    /**
     * 任务类型 (ANALYSIS, DOWNLOAD, OFFLINE, MAINTENANCE, etc.)
     */
    public final QueryColumn TASK_TYPE = new QueryColumn(this, "task_type");

    /**
     * 任务状态 (PENDING, RUNNING, COMPLETED, FAILED, CANCELLED)
     */
    public final QueryColumn TASK_STATUS = new QueryColumn(this, "task_status");

    /**
     * 任务优先级 (LOW, NORMAL, HIGH, URGENT)
     */
    public final QueryColumn PRIORITY = new QueryColumn(this, "priority");

    /**
     * 创建时间
     */
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 计划开始时间
     */
    public final QueryColumn SCHEDULED_START_TIME = new QueryColumn(this, "scheduled_start_time");

    /**
     * 实际开始时间
     */
    public final QueryColumn ACTUAL_START_TIME = new QueryColumn(this, "actual_start_time");

    /**
     * 计划结束时间
     */
    public final QueryColumn SCHEDULED_END_TIME = new QueryColumn(this, "scheduled_end_time");

    /**
     * 实际结束时间
     */
    public final QueryColumn ACTUAL_END_TIME = new QueryColumn(this, "actual_end_time");

    /**
     * 进度百分比 (0-100)
     */
    public final QueryColumn PROGRESS_PERCENT = new QueryColumn(this, "progress_percent");

    /**
     * 会话数量
     */
    public final QueryColumn SESSION_COUNT = new QueryColumn(this, "session_count");

    /**
     * 数据大小（字节）
     */
    public final QueryColumn DATA_SIZE = new QueryColumn(this, "data_size");

    /**
     * 任务配置参数（JSON格式）
     */
    public final QueryColumn TASK_CONFIG = new QueryColumn(this, "task_config");

    /**
     * Cron表达式（定时任务）
     */
    public final QueryColumn CRON_EXPRESSION = new QueryColumn(this, "cron_expression");

    /**
     * 重试次数
     */
    public final QueryColumn RETRY_COUNT = new QueryColumn(this, "retry_count");

    /**
     * 当前重试次数
     */
    public final QueryColumn CURRENT_RETRY_COUNT = new QueryColumn(this, "current_retry_count");

    /**
     * 超时时间（秒）
     */
    public final QueryColumn TIMEOUT_SECONDS = new QueryColumn(this, "timeout_seconds");

    /**
     * 错误信息
     */
    public final QueryColumn ERROR_MESSAGE = new QueryColumn(this, "error_message");

    /**
     * 执行节点
     */
    public final QueryColumn EXECUTION_NODE = new QueryColumn(this, "execution_node");

    /**
     * 是否启用
     */
    public final QueryColumn ENABLED = new QueryColumn(this, "enabled");

    /**
     * 创建用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 创建用户名
     */
    public final QueryColumn USER_NAME = new QueryColumn(this, "user_name");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 是否删除 (0: 未删除, 1: 已删除)
     */
    public final QueryColumn DELETED = new QueryColumn(this, "deleted");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认构造函数
     */
    public TaskTableDef() {
        super("tb_task", "tb_task");
    }

    /**
     * 带别名的构造函数
     */
    public TaskTableDef(String alias) {
        super("tb_task", alias);
    }

    /**
     * 获取表名
     */
    @Override
    public String getTableName() {
        return "tb_task";
    }
}