package com.geeksec.task.domain.event;

import lombok.Getter;

/**
 * PCAP下载任务失败事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public class PcapDownloadTaskFailedEvent extends PcapDownloadEvent {

    /**
     * 错误信息
     */
    private final String errorMessage;

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 异常堆栈信息
     */
    private final String stackTrace;

    /**
     * 是否可重试
     */
    private final Boolean retryable;

    /**
     * 重试次数
     */
    private final Integer retryCount;

    public PcapDownloadTaskFailedEvent(Object source, Integer taskId, String userId, 
                                     String errorMessage, String errorCode, 
                                     String stackTrace, Boolean retryable, Integer retryCount) {
        super(source, taskId, userId, "PCAP下载任务失败");
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.stackTrace = stackTrace;
        this.retryable = retryable;
        this.retryCount = retryCount;
    }
}
