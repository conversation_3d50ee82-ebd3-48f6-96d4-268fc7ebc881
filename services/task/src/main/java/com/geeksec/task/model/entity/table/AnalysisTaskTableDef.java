package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 分析任务表定义类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 * @note 此类由 MyBatis-Flex 注解处理器自动生成
 */
public class AnalysisTaskTableDef extends TableDef {

    /**
     * 分析任务表定义
     */
    public static final AnalysisTaskTableDef ANALYSIS_TASK = new AnalysisTaskTableDef();

    /**
     * 主键
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 任务名称
     */
    public final QueryColumn TASK_NAME = new QueryColumn(this, "task_name");

    /**
     * 网络流量信息
     */
    public final QueryColumn NET_FLOW = new QueryColumn(this, "netflow");

    /**
     * 任务备注
     */
    public final QueryColumn TASK_REMARK = new QueryColumn(this, "task_remark");

    /**
     * 任务状态
     */
    public final QueryColumn TASK_STATE = new QueryColumn(this, "task_state");

    /**
     * 最后暂停时间
     */
    public final QueryColumn LAST_SUSPEND_TIME = new QueryColumn(this, "last_suspend_time");

    /**
     * 暂停次数
     */
    public final QueryColumn SUSPEND_TIMES = new QueryColumn(this, "suspend_times");

    /**
     * 用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 分析任务类型 (1: 实时分析任务-从NIC采集流量数据, 2: 离线分析任务-读取pcap文件)
     */
    public final QueryColumn TASK_TYPE = new QueryColumn(this, "task_type");

    /**
     * 创建时间
     */
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 是否删除 (0: 未删除, 1: 已删除)
     */
    public final QueryColumn DELETED = new QueryColumn(this, "deleted");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认构造函数
     */
    public AnalysisTaskTableDef() {
        super("tb_task_analysis");
    }

    /**
     * 带别名的构造函数
     */
    public AnalysisTaskTableDef(String alias) {
        super("tb_task_analysis", alias);
    }

}