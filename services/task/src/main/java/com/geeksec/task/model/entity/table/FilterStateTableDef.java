package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 过滤状态实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public class FilterStateTableDef extends TableDef {

    /**
     * 过滤状态实体表定义实例
     */
    public static final FilterStateTableDef FILTER_STATE = new FilterStateTableDef();

    /**
     * 主键
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 过滤状态 (0: 未启用, 1: 已启用, 2: 暂停, 3: 错误)
     */
    public final QueryColumn STATE = new QueryColumn(this, "state");

    /**
     * 过滤配置JSON
     */
    public final QueryColumn CONFIG = new QueryColumn(this, "config");

    /**
     * 过滤规则版本
     */
    public final QueryColumn RULE_VERSION = new QueryColumn(this, "rule_version");

    /**
     * 最后同步时间
     */
    public final QueryColumn LAST_SYNC_TIME = new QueryColumn(this, "last_sync_time");

    /**
     * 同步状态 (0: 未同步, 1: 同步成功, 2: 同步失败)
     */
    public final QueryColumn SYNC_STATUS = new QueryColumn(this, "sync_status");

    /**
     * 同步错误信息
     */
    public final QueryColumn SYNC_ERROR = new QueryColumn(this, "sync_error");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 创建者用户ID
     */
    public final QueryColumn CREATED_BY = new QueryColumn(this, "created_by");

    /**
     * 更新者用户ID
     */
    public final QueryColumn UPDATED_BY = new QueryColumn(this, "updated_by");

    /**
     * 构造函数
     */
    public FilterStateTableDef() {
        super("", "tb_filter_state");
    }

    /**
     * 带别名的构造函数
     */
    public FilterStateTableDef(String alias) {
        super("", "tb_filter_state", alias);
    }
}