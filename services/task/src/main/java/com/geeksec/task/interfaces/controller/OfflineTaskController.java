package com.geeksec.task.interfaces.controller;

import com.geeksec.task.application.service.OfflineTaskService;
import com.geeksec.task.model.condition.OfflineTaskQueryCondition;
import com.geeksec.task.model.dto.OfflineTaskDto;
import com.geeksec.task.model.dto.OfflineTaskDeleteDto;
import com.geeksec.task.model.vo.OfflineTaskVo;
import com.geeksec.task.model.vo.OfflineTaskPageVo;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 离线分析任务控制器
 * 用于管理离线分析任务（读取pcap文件进行流量分析）
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/offline-task")
@RequiredArgsConstructor
@Validated
@Tag(name = "离线分析任务管理", description = "离线分析任务的增删改查操作")
public class OfflineTaskController {

    private final OfflineTaskService offlineTaskService;

    /**
     * 获取当前用户最新的离线分析任务
     */
    @GetMapping("/latest")
    @Operation(summary = "获取最新离线分析任务", description = "获取当前用户的最新离线分析任务信息")
    public ApiResponse<OfflineTaskVo> getLatestTask() {
        try {
            OfflineTaskVo taskVo = offlineTaskService.getLastTask();
            return ApiResponse.success(taskVo);
        } catch (Exception e) {
            log.error("获取最新任务失败", e);
            return ApiResponse.error(500, "获取最新任务失败：" + e.getMessage());
        }
    }

    /**
     * 根据任务ID获取离线分析任务详情
     */
    @GetMapping("/{taskId}")
    @Operation(summary = "获取离线分析任务详情", description = "根据任务ID获取离线分析任务详细信息")
    public ApiResponse<OfflineTaskVo> getTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId) {
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID不能为空或无效");
            }

            OfflineTaskVo taskVo = offlineTaskService.getTask(taskId);
            if (taskVo == null) {
                return ApiResponse.error(404, "任务不存在");
            }

            return ApiResponse.success(taskVo);
        } catch (Exception e) {
            log.error("获取任务详情失败，任务ID：{}", taskId, e);
            return ApiResponse.error(500, "获取任务详情失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询任务列表
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询任务", description = "根据条件分页查询任务列表")
    public ApiResponse<PageResultVo<OfflineTaskPageVo>> pageTask(
            @Parameter(description = "查询条件", required = true)
            @RequestBody @Valid OfflineTaskQueryCondition condition) {
        try {
            if (condition == null) {
                return ApiResponse.badRequest("查询条件不能为空");
            }

            OfflineTaskService.TaskPageResult taskPageResult = offlineTaskService.pageTask(condition);
            PageResultVo<OfflineTaskPageVo> pageResultVo = new PageResultVo<>(
                taskPageResult.getData(),
                taskPageResult.getTotal(),
                taskPageResult.getCurrentPage(),
                taskPageResult.getPageSize()
            );
            return ApiResponse.success(pageResultVo);
        } catch (Exception e) {
            log.error("分页查询任务失败", e);
            return ApiResponse.error(500, "分页查询任务失败：" + e.getMessage());
        }
    }

    /**
     * 创建任务
     */
    @PostMapping
    @Operation(summary = "创建任务", description = "创建新的离线任务")
    public ApiResponse<Integer> addTask(
            @Parameter(description = "任务信息", required = true)
            @RequestBody @Valid OfflineTaskDto dto) {
        try {
            OfflineTaskService.TaskOperationResult result = offlineTaskService.addTask(dto);
            if (result.isSuccess()) {
                return ApiResponse.success(result.getMessage(), (Integer) result.getData());
            } else {
                return ApiResponse.error(400, result.getMessage());
            }
        } catch (Exception e) {
            log.error("创建任务失败", e);
            return ApiResponse.error(500, "创建任务失败：" + e.getMessage());
        }
    }

    /**
     * 更新任务
     */
    @PutMapping
    @Operation(summary = "更新任务", description = "更新现有的离线任务")
    public ApiResponse<Void> updateTask(
            @Parameter(description = "任务信息", required = true)
            @RequestBody @Valid OfflineTaskDto dto) {
        try {
            if (dto.getTaskId() == null) {
                return ApiResponse.badRequest("任务ID不能为空");
            }

            OfflineTaskService.TaskOperationResult result = offlineTaskService.updateTask(dto);
            if (result.isSuccess()) {
                return ApiResponse.success();
            } else {
                return ApiResponse.error(400, result.getMessage());
            }
        } catch (Exception e) {
            log.error("更新任务失败", e);
            return ApiResponse.error(500, "更新任务失败：" + e.getMessage());
        }
    }

    /**
     * 删除任务
     */
    @DeleteMapping
    @Operation(summary = "删除任务", description = "删除指定的离线任务")
    public ApiResponse<Void> deleteTask(
            @Parameter(description = "删除参数", required = true)
            @RequestBody @Valid OfflineTaskDeleteDto dto) {
        try {
            OfflineTaskService.TaskOperationResult result = offlineTaskService.deleteTask(dto);
            if (result.isSuccess()) {
                return ApiResponse.success();
            } else {
                return ApiResponse.error(400, result.getMessage());
            }
        } catch (Exception e) {
            log.error("删除任务失败", e);
            return ApiResponse.error(500, "删除任务失败：" + e.getMessage());
        }
    }

    /**
     * 启动任务
     */
    @PostMapping("/{taskId}/start")
    @Operation(summary = "启动任务", description = "启动指定的离线任务")
    public ApiResponse<Void> startTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId) {
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID不能为空或无效");
            }

            OfflineTaskService.TaskOperationResult result = offlineTaskService.startTask(taskId);
            if (result.isSuccess()) {
                return ApiResponse.success();
            } else {
                return ApiResponse.error(400, result.getMessage());
            }
        } catch (Exception e) {
            log.error("启动任务失败，任务ID：{}", taskId, e);
            return ApiResponse.error(500, "启动任务失败：" + e.getMessage());
        }
    }

    /**
     * 停止任务
     */
    @PostMapping("/{taskId}/stop")
    @Operation(summary = "停止任务", description = "停止指定的离线任务")
    public ApiResponse<Void> stopTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId) {
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID不能为空或无效");
            }

            OfflineTaskService.TaskOperationResult result = offlineTaskService.stopTask(taskId);
            if (result.isSuccess()) {
                return ApiResponse.success();
            } else {
                return ApiResponse.error(400, result.getMessage());
            }
        } catch (Exception e) {
            log.error("停止任务失败，任务ID：{}", taskId, e);
            return ApiResponse.error(500, "停止任务失败：" + e.getMessage());
        }
    }


}
