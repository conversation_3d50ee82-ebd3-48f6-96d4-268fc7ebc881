package com.geeksec.task.domain.event;

import com.geeksec.task.model.entity.PcapDownloadTask;
import lombok.Getter;

import java.util.List;

/**
 * PCAP下载任务创建事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public class PcapDownloadTaskCreatedEvent extends PcapDownloadEvent {

    /**
     * 下载任务实体
     */
    private final PcapDownloadTask task;

    /**
     * 会话ID列表
     */
    private final List<String> sessionIds;

    /**
     * 任务优先级
     */
    private final Integer priority;

    public PcapDownloadTaskCreatedEvent(Object source, PcapDownloadTask task, List<String> sessionIds) {
        super(source, task.getId(), task.getUserId(), "PCAP下载任务已创建");
        this.task = task;
        this.sessionIds = sessionIds;
        this.priority = calculatePriority(sessionIds.size());
    }

    /**
     * 根据会话数量计算任务优先级
     * 会话数量越少，优先级越高
     */
    private Integer calculatePriority(int sessionCount) {
        if (sessionCount <= 10) {
            return 1; // 高优先级
        } else if (sessionCount <= 100) {
            return 2; // 中优先级
        } else {
            return 3; // 低优先级
        }
    }
}
