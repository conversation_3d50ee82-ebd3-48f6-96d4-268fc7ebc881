package com.geeksec.task.repository;

import com.geeksec.task.model.entity.PcapDownloadTask;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP下载任务数据访问接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface PcapDownloadTaskRepository extends BaseMapper<PcapDownloadTask> {

    /**
     * 获取用户的下载任务列表
     * 
     * @param userId 用户ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM pcap_download_task WHERE user_id = #{userId} " +
            "ORDER BY create_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<PcapDownloadTask> getUserTasks(@Param("userId") String userId, 
                                       @Param("offset") int offset, 
                                       @Param("limit") int limit);

    /**
     * 获取用户的下载任务列表（分页）
     * 
     * @param userId 用户ID
     * @param page 页码（从1开始）
     * @param size 页大小
     * @return 任务列表
     */
    default List<PcapDownloadTask> getUserTasks(String userId, int page, int size) {
        int offset = (page - 1) * size;
        return getUserTasks(userId, offset, size);
    }

    /**
     * 获取待处理的任务
     * 
     * @return 待处理任务列表
     */
    @Select("SELECT * FROM pcap_download_task WHERE status = 1 ORDER BY create_time ASC")
    List<PcapDownloadTask> getPendingTasks();

    /**
     * 获取下一个待处理任务
     *
     * @return 待处理任务
     */
    @Select("SELECT * FROM pcap_download_task WHERE status = 1 ORDER BY create_time ASC LIMIT 1")
    PcapDownloadTask getNextPendingTask();

    /**
     * 获取过期的任务
     *
     * @param expireTime 过期时间
     * @return 过期任务列表
     */
    @Select("SELECT * FROM pcap_download_task WHERE expire_time < #{expireTime}")
    List<PcapDownloadTask> getExpiredTasks(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 获取用户任务数量
     *
     * @param userId 用户ID
     * @return 任务数量
     */
    @Select("SELECT COUNT(*) FROM pcap_download_task WHERE user_id = #{userId}")
    long getUserTaskCount(@Param("userId") String userId);

    /**
     * 获取指定状态的任务数量
     *
     * @param status 任务状态
     * @return 任务数量
     */
    @Select("SELECT COUNT(*) FROM pcap_download_task WHERE status = #{status}")
    long getTaskCountByStatus(@Param("status") int status);

    /**
     * 获取用户指定状态的任务
     *
     * @param userId 用户ID
     * @param status 任务状态
     * @return 任务列表
     */
    @Select("SELECT * FROM pcap_download_task WHERE user_id = #{userId} AND status = #{status} " +
            "ORDER BY create_time DESC")
    List<PcapDownloadTask> getUserTasksByStatus(@Param("userId") String userId,
                                               @Param("status") int status);

    /**
     * 删除用户的所有任务
     *
     * @param userId 用户ID
     * @return 删除的任务数量
     */
    @Select("DELETE FROM pcap_download_task WHERE user_id = #{userId}")
    int deleteUserTasks(@Param("userId") String userId);

    /**
     * 清理指定时间之前的任务
     *
     * @param beforeTime 时间点
     * @return 删除的任务数量
     */
    @Select("DELETE FROM pcap_download_task WHERE create_time < #{beforeTime}")
    int cleanupTasksBefore(@Param("beforeTime") LocalDateTime beforeTime);
}
