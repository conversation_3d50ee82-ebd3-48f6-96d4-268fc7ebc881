package com.geeksec.task.infrastructure.processor;

import com.geeksec.task.model.entity.PcapDownloadTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * PCAP文件处理器
 * 负责PCAP文件的下载、压缩和打包
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PcapFileProcessor {

    @Value("${app.pcap.storage.base-path:/data/pcap}")
    private String pcapBasePath;

    @Value("${app.pcap.download.temp-path:/tmp/pcap-download}")
    private String tempDownloadPath;

    @Value("${app.pcap.download.max-file-size:1073741824}") // 1GB
    private long maxFileSize;

    /**
     * 处理PCAP下载任务
     *
     * @param task 下载任务
     * @param pcapFilePaths PCAP文件路径列表
     * @return 合并后的PCAP文件路径
     */
    public String processPcapDownload(PcapDownloadTask task, List<String> pcapFilePaths) {
        log.info("开始处理PCAP下载任务: taskId={}, fileCount={}", task.getId(), pcapFilePaths.size());

        try {
            // 创建临时目录
            Path tempDir = createTempDirectory(task);

            // 合并PCAP文件
            String mergedFileName = generateMergedFileName(task);
            Path mergedPath = tempDir.resolve(mergedFileName);

            long totalSize = mergePcapFiles(pcapFilePaths, mergedPath, task);

            log.info("PCAP文件合并完成: taskId={}, mergedPath={}, size={}",
                    task.getId(), mergedPath, totalSize);

            return mergedPath.toString();

        } catch (Exception e) {
            log.error("处理PCAP下载任务失败: taskId={}", task.getId(), e);
            throw new RuntimeException("处理PCAP下载任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 压缩PCAP文件
     */
    private long compressPcapFiles(List<String> pcapFilePaths, Path archivePath, PcapDownloadTask task) 
            throws IOException {
        long totalSize = 0;
        int processedCount = 0;

        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(archivePath))) {
            zipOut.setLevel(6); // 设置压缩级别

            for (String pcapFilePath : pcapFilePaths) {
                try {
                    Path pcapPath = Paths.get(pcapBasePath, pcapFilePath);
                    
                    if (!Files.exists(pcapPath)) {
                        log.warn("PCAP文件不存在: {}", pcapPath);
                        continue;
                    }

                    long fileSize = Files.size(pcapPath);
                    if (fileSize > maxFileSize) {
                        log.warn("PCAP文件过大，跳过: {} ({}字节)", pcapPath, fileSize);
                        continue;
                    }

                    // 添加文件到压缩包
                    String entryName = generateEntryName(pcapFilePath, processedCount);
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zipOut.putNextEntry(zipEntry);

                    // 复制文件内容
                    Files.copy(pcapPath, zipOut);
                    zipOut.closeEntry();

                    totalSize += fileSize;
                    processedCount++;

                    // 更新进度
                    updateProgress(task, processedCount, pcapFilePaths.size());

                    log.debug("已添加PCAP文件到压缩包: {} -> {}", pcapPath, entryName);

                } catch (Exception e) {
                    log.error("处理PCAP文件失败: {}", pcapFilePath, e);
                    // 继续处理其他文件
                }
            }
        }

        return totalSize;
    }

    /**
     * 创建临时目录
     */
    private Path createTempDirectory(PcapDownloadTask task) throws IOException {
        Path tempDir = Paths.get(tempDownloadPath, "task-" + task.getId());
        Files.createDirectories(tempDir);
        return tempDir;
    }

    /**
     * 生成合并后的PCAP文件名
     */
    private String generateMergedFileName(PcapDownloadTask task) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("merged_pcap_%s_%s.pcap", task.getAlarmType(), timestamp);
    }

    /**
     * 合并PCAP文件
     */
    private long mergePcapFiles(List<String> pcapFilePaths, Path mergedPath, PcapDownloadTask task)
            throws IOException {
        log.info("开始合并PCAP文件: taskId={}, fileCount={}", task.getId(), pcapFilePaths.size());

        try {
            // 构建mergecap命令
            List<String> command = new ArrayList<>();
            command.add("mergecap");
            command.add("-w");
            command.add(mergedPath.toString());

            // 添加所有PCAP文件路径
            for (String pcapFilePath : pcapFilePaths) {
                Path fullPath = Paths.get(pcapBasePath, pcapFilePath);
                if (Files.exists(fullPath)) {
                    command.add(fullPath.toString());
                } else {
                    log.warn("PCAP文件不存在，跳过: {}", fullPath);
                }
            }

            if (command.size() <= 3) { // 只有命令和输出文件，没有输入文件
                throw new RuntimeException("没有有效的PCAP文件可以合并");
            }

            // 执行mergecap命令
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            // 读取命令输出
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug("mergecap输出: {}", line);
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("mergecap命令执行失败，退出码: " + exitCode);
            }

            // 检查合并后的文件
            if (!Files.exists(mergedPath)) {
                throw new RuntimeException("合并后的PCAP文件不存在");
            }

            long fileSize = Files.size(mergedPath);
            log.info("PCAP文件合并完成: taskId={}, fileSize={}", task.getId(), fileSize);

            return fileSize;

        } catch (Exception e) {
            log.error("合并PCAP文件失败: taskId={}", task.getId(), e);
            throw new RuntimeException("合并PCAP文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成压缩包内文件名
     */
    private String generateEntryName(String pcapFilePath, int index) {
        Path path = Paths.get(pcapFilePath);
        String fileName = path.getFileName().toString();
        
        // 如果文件名不包含扩展名，添加.pcap扩展名
        if (!fileName.toLowerCase().endsWith(".pcap")) {
            fileName += ".pcap";
        }
        
        // 添加序号前缀
        return String.format("%03d_%s", index + 1, fileName);
    }

    /**
     * 更新任务进度
     */
    private void updateProgress(PcapDownloadTask task, int processedCount, int totalCount) {
        int progress = (int) ((double) processedCount / totalCount * 100);
        log.debug("任务进度更新: taskId={}, progress={}%, processed={}/{}", 
                task.getId(), progress, processedCount, totalCount);
        
        // TODO: 调用服务更新数据库中的进度
        // pcapDownloadService.updateProgress(task.getId(), progress, processedCount);
    }

    /**
     * 清理临时文件
     * 
     * @param task 下载任务
     */
    public void cleanupTempFiles(PcapDownloadTask task) {
        try {
            Path tempDir = Paths.get(tempDownloadPath, "task-" + task.getId());
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                        .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                        .forEach(path -> {
                            try {
                                Files.deleteIfExists(path);
                            } catch (IOException e) {
                                log.warn("删除临时文件失败: {}", path, e);
                            }
                        });
                log.info("清理临时文件完成: taskId={}", task.getId());
            }
        } catch (Exception e) {
            log.error("清理临时文件失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 验证PCAP文件
     * 
     * @param filePath 文件路径
     * @return 是否有效
     */
    public boolean validatePcapFile(String filePath) {
        try {
            Path path = Paths.get(pcapBasePath, filePath);
            
            if (!Files.exists(path)) {
                return false;
            }

            if (!Files.isReadable(path)) {
                return false;
            }

            long fileSize = Files.size(path);
            if (fileSize == 0 || fileSize > maxFileSize) {
                return false;
            }

            // TODO: 可以添加更详细的PCAP文件格式验证
            return true;

        } catch (Exception e) {
            log.error("验证PCAP文件失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public long getFileSize(String filePath) {
        try {
            Path path = Paths.get(pcapBasePath, filePath);
            return Files.exists(path) ? Files.size(path) : 0L;
        } catch (Exception e) {
            log.error("获取文件大小失败: {}", filePath, e);
            return 0L;
        }
    }
}
