package com.geeksec.task.infrastructure.processor;

import com.geeksec.task.model.entity.PcapDownloadTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * PCAP文件处理器 - 重新实现
 * 负责PCAP文件的下载、合并和打包
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PcapFileProcessor {

    @Value("${app.pcap.storage.base-path:/data}")
    private String pcapBasePath;

    @Value("${app.pcap.download.temp-path:/tmp/pcap-download}")
    private String tempDownloadPath;

    @Value("${app.pcap.download.max-file-size:1073741824}") // 1GB
    private long maxFileSize;

    @Value("${app.pcap.mergecap.command:/usr/bin/mergecap}")
    private String mergecapCommand;

    @Value("${app.pcap.mergecap.timeout:300}") // 5分钟超时
    private long mergecapTimeoutSeconds;

    /**
     * 处理PCAP下载任务
     */
    public String processPcapDownload(PcapDownloadTask task, List<String> pcapFilePaths) {
        log.info("开始处理PCAP下载任务: taskId={}, fileCount={}", task.getId(), pcapFilePaths.size());

        try {
            // 创建临时目录
            Path tempDir = createTempDirectory(task);

            // 合并PCAP文件
            String mergedFileName = generateMergedFileName(task);
            Path mergedPath = tempDir.resolve(mergedFileName);

            long totalSize = mergePcapFiles(pcapFilePaths, mergedPath, task);

            log.info("PCAP文件合并完成: taskId={}, mergedPath={}, size={}",
                    task.getId(), mergedPath, totalSize);

            return mergedPath.toString();

        } catch (Exception e) {
            log.error("处理PCAP下载任务失败: taskId={}", task.getId(), e);
            throw new RuntimeException("处理PCAP下载任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 合并PCAP文件 - 使用mergecap工具
     */
    private long mergePcapFiles(List<String> pcapFilePaths, Path mergedPath, PcapDownloadTask task) {
        log.info("开始合并PCAP文件: taskId={}, fileCount={}", task.getId(), pcapFilePaths.size());

        try {
            // 创建临时目录存放有效的PCAP文件
            Path tempDir = mergedPath.getParent().resolve("temp_pcap_files");
            Files.createDirectories(tempDir);

            // 验证并复制有效的PCAP文件到临时目录
            List<String> validPcapFiles = validateAndCopyPcapFiles(pcapFilePaths, tempDir, task);
            
            if (validPcapFiles.isEmpty()) {
                throw new RuntimeException("没有找到有效的PCAP文件");
            }

            // 使用mergecap工具合并PCAP文件
            long mergedFileSize = executeMergecap(validPcapFiles, mergedPath, task);

            // 清理临时目录
            cleanupTempDirectory(tempDir);

            log.info("PCAP文件合并完成: taskId={}, mergedSize={}", task.getId(), mergedFileSize);
            return mergedFileSize;

        } catch (Exception e) {
            log.error("合并PCAP文件失败: taskId={}", task.getId(), e);
            throw new RuntimeException("合并PCAP文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证并复制有效的PCAP文件
     */
    private List<String> validateAndCopyPcapFiles(List<String> pcapFilePaths, Path tempDir, PcapDownloadTask task) {
        List<String> validFiles = new ArrayList<>();
        int processedCount = 0;

        for (String pcapFilePath : pcapFilePaths) {
            try {
                Path sourcePath = Paths.get(pcapFilePath);
                
                if (!Files.exists(sourcePath)) {
                    log.warn("PCAP文件不存在: {}", sourcePath);
                    continue;
                }

                long fileSize = Files.size(sourcePath);
                if (fileSize == 0) {
                    log.warn("PCAP文件为空，跳过: {}", sourcePath);
                    continue;
                }

                if (fileSize > maxFileSize) {
                    log.warn("PCAP文件过大，跳过: {} ({}字节)", sourcePath, fileSize);
                    continue;
                }

                // 验证PCAP文件格式
                if (!isValidPcapFile(sourcePath)) {
                    log.warn("无效的PCAP文件格式，跳过: {}", sourcePath);
                    continue;
                }

                // 复制到临时目录
                String fileName = String.format("pcap_%d_%s.pcap", processedCount, 
                                               sourcePath.getFileName().toString().replaceAll("[^a-zA-Z0-9._-]", "_"));
                Path targetPath = tempDir.resolve(fileName);
                Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                
                validFiles.add(targetPath.toString());
                processedCount++;

                log.debug("已复制有效PCAP文件: {} -> {}", sourcePath, targetPath);

            } catch (Exception e) {
                log.error("处理PCAP文件失败: {}", pcapFilePath, e);
            }
        }

        return validFiles;
    }

    /**
     * 执行mergecap命令合并PCAP文件
     */
    private long executeMergecap(List<String> validPcapFiles, Path mergedPath, PcapDownloadTask task) throws Exception {
        // 构建mergecap命令
        List<String> command = new ArrayList<>();
        command.add(mergecapCommand);
        command.add("-w");
        command.add(mergedPath.toString());
        command.addAll(validPcapFiles);

        log.info("执行mergecap命令: taskId={}, fileCount={}", task.getId(), validPcapFiles.size());

        // 执行命令
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);
        
        Process process = processBuilder.start();

        // 读取命令输出
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
                log.debug("mergecap输出: {}", line);
            }
        }

        // 等待命令完成
        boolean finished = process.waitFor(mergecapTimeoutSeconds, TimeUnit.SECONDS);
        if (!finished) {
            process.destroyForcibly();
            throw new RuntimeException("mergecap命令执行超时");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new RuntimeException("mergecap命令执行失败，退出码: " + exitCode + ", 输出: " + output);
        }

        // 检查合并后的文件
        if (!Files.exists(mergedPath)) {
            throw new RuntimeException("合并后的PCAP文件不存在");
        }

        return Files.size(mergedPath);
    }

    /**
     * 验证PCAP文件格式
     */
    private boolean isValidPcapFile(Path filePath) {
        try {
            // 读取文件头部字节来验证PCAP格式
            byte[] header = new byte[4];
            try (InputStream is = Files.newInputStream(filePath)) {
                int bytesRead = is.read(header);
                if (bytesRead < 4) {
                    return false;
                }
            }

            // 检查PCAP魔数
            // 0xA1B2C3D4 (little-endian) 或 0xD4C3B2A1 (big-endian)
            return (header[0] == (byte)0xA1 && header[1] == (byte)0xB2 && 
                    header[2] == (byte)0xC3 && header[3] == (byte)0xD4) ||
                   (header[0] == (byte)0xD4 && header[1] == (byte)0xC3 && 
                    header[2] == (byte)0xB2 && header[3] == (byte)0xA1);

        } catch (Exception e) {
            log.debug("验证PCAP文件格式失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 创建临时目录
     */
    private Path createTempDirectory(PcapDownloadTask task) throws IOException {
        Path tempDir = Paths.get(tempDownloadPath, "task-" + task.getId());
        Files.createDirectories(tempDir);
        return tempDir;
    }

    /**
     * 生成合并后的文件名
     */
    private String generateMergedFileName(PcapDownloadTask task) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return String.format("merged_pcap_%s_%s.pcap", task.getId(), timestamp);
    }

    /**
     * 清理临时目录
     */
    public void cleanupTempFiles(PcapDownloadTask task) {
        try {
            Path tempDir = Paths.get(tempDownloadPath, "task-" + task.getId());
            if (Files.exists(tempDir)) {
                cleanupTempDirectory(tempDir);
            }
        } catch (Exception e) {
            log.error("清理临时文件失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 递归删除目录
     */
    private void cleanupTempDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除文件失败: {}", path, e);
                        }
                    });
        }
    }
}
