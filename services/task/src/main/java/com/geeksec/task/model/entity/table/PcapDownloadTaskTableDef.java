package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * PCAP下载任务表定义类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 * @note 此类由 MyBatis-Flex 注解处理器自动生成
 */
public class PcapDownloadTaskTableDef extends TableDef {

    /**
     * PCAP下载任务表定义
     */
    public static final PcapDownloadTaskTableDef PCAP_DOWNLOAD_TASK = new PcapDownloadTaskTableDef();

    /**
     * 主键
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 任务ID（关联tb_task表）
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 创建者用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 告警类型
     */
    public final QueryColumn ALARM_TYPE = new QueryColumn(this, "alarm_type");

    /**
     * 告警时间
     */
    public final QueryColumn ALARM_TIME = new QueryColumn(this, "alarm_time");

    /**
     * 会话ID列表（JSON格式）
     */
    public final QueryColumn SESSION_IDS = new QueryColumn(this, "session_ids");

    /**
     * PCAP文件路径列表（JSON格式）
     */
    public final QueryColumn PCAP_FILE_PATHS = new QueryColumn(this, "pcap_file_paths");

    /**
     * 压缩包文件路径
     */
    public final QueryColumn ARCHIVE_FILE_PATH = new QueryColumn(this, "archive_file_path");

    /**
     * 压缩包文件大小（字节）
     */
    public final QueryColumn ARCHIVE_FILE_SIZE = new QueryColumn(this, "archive_file_size");

    /**
     * 总文件数量
     */
    public final QueryColumn TOTAL_FILE_COUNT = new QueryColumn(this, "total_file_count");

    /**
     * 已处理文件数量
     */
    public final QueryColumn PROCESSED_FILE_COUNT = new QueryColumn(this, "processed_file_count");

    /**
     * 任务状态
     */
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 进度百分比 (0-100)
     */
    public final QueryColumn PROGRESS = new QueryColumn(this, "progress");

    /**
     * 错误信息
     */
    public final QueryColumn ERROR_MESSAGE = new QueryColumn(this, "error_message");

    /**
     * 下载URL
     */
    public final QueryColumn DOWNLOAD_URL = new QueryColumn(this, "download_url");

    /**
     * 文件过期时间
     */
    public final QueryColumn EXPIRE_TIME = new QueryColumn(this, "expire_time");

    /**
     * 创建时间
     */
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 开始处理时间
     */
    public final QueryColumn START_TIME = new QueryColumn(this, "start_time");

    /**
     * 完成时间
     */
    public final QueryColumn COMPLETE_TIME = new QueryColumn(this, "complete_time");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认构造函数
     */
    public PcapDownloadTaskTableDef() {
        super("tb_pcap_download_task");
    }

    /**
     * 带别名的构造函数
     */
    public PcapDownloadTaskTableDef(String alias) {
        super("tb_pcap_download_task", alias);
    }

}