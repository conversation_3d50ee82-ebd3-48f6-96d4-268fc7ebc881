package com.geeksec.task.infrastructure.service;

import com.geeksec.task.infrastructure.processor.PcapDownloadTaskProcessor.FileUploadService;
import com.geeksec.task.model.entity.PcapDownloadTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件上传服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileUploadServiceImpl implements FileUploadService {

    @Value("${app.file.upload.base-path:/data/downloads}")
    private String uploadBasePath;

    @Value("${app.file.download.base-url:http://localhost:8080/downloads}")
    private String downloadBaseUrl;

    @Value("${app.file.upload.max-size:5368709120}") // 5GB
    private long maxFileSize;

    @Override
    public String uploadArchiveFile(String archivePath, PcapDownloadTask task) {
        log.info("开始上传压缩包文件: taskId={}, archivePath={}", task.getId(), archivePath);

        try {
            Path sourcePath = Paths.get(archivePath);
            
            // 验证文件
            validateFile(sourcePath);

            // 生成目标路径
            String targetPath = generateTargetPath(task, sourcePath.getFileName().toString());
            Path targetFullPath = Paths.get(uploadBasePath, targetPath);

            // 创建目标目录
            Files.createDirectories(targetFullPath.getParent());

            // 移动文件到目标位置
            Files.move(sourcePath, targetFullPath);

            // 生成下载URL
            String downloadUrl = generateDownloadUrl(targetPath);

            log.info("压缩包文件上传完成: taskId={}, targetPath={}, downloadUrl={}", 
                    task.getId(), targetFullPath, downloadUrl);

            return downloadUrl;

        } catch (Exception e) {
            log.error("上传压缩包文件失败: taskId={}, archivePath={}", task.getId(), archivePath, e);
            throw new RuntimeException("上传文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(Path filePath) throws IOException {
        if (!Files.exists(filePath)) {
            throw new IllegalArgumentException("文件不存在: " + filePath);
        }

        if (!Files.isReadable(filePath)) {
            throw new IllegalArgumentException("文件不可读: " + filePath);
        }

        long fileSize = Files.size(filePath);
        if (fileSize == 0) {
            throw new IllegalArgumentException("文件为空: " + filePath);
        }

        if (fileSize > maxFileSize) {
            throw new IllegalArgumentException(
                String.format("文件过大: %d字节，最大允许: %d字节", fileSize, maxFileSize)
            );
        }
    }

    /**
     * 生成目标路径
     */
    private String generateTargetPath(PcapDownloadTask task, String fileName) {
        // 按日期和用户ID组织目录结构
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String userDir = "user-" + task.getUserId();
        
        return String.format("pcap/%s/%s/task-%d_%s", dateStr, userDir, task.getId(), fileName);
    }

    /**
     * 生成下载URL
     */
    private String generateDownloadUrl(String targetPath) {
        return downloadBaseUrl + "/" + targetPath;
    }

    /**
     * 删除文件
     * 
     * @param downloadUrl 下载URL
     * @return 是否成功
     */
    public boolean deleteFile(String downloadUrl) {
        try {
            if (downloadUrl == null || !downloadUrl.startsWith(downloadBaseUrl)) {
                return false;
            }

            String relativePath = downloadUrl.substring(downloadBaseUrl.length() + 1);
            Path filePath = Paths.get(uploadBasePath, relativePath);

            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("文件删除成功: {}", filePath);
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("删除文件失败: downloadUrl={}", downloadUrl, e);
            return false;
        }
    }

    /**
     * 获取文件大小
     * 
     * @param downloadUrl 下载URL
     * @return 文件大小（字节）
     */
    public long getFileSize(String downloadUrl) {
        try {
            if (downloadUrl == null || !downloadUrl.startsWith(downloadBaseUrl)) {
                return 0L;
            }

            String relativePath = downloadUrl.substring(downloadBaseUrl.length() + 1);
            Path filePath = Paths.get(uploadBasePath, relativePath);

            return Files.exists(filePath) ? Files.size(filePath) : 0L;

        } catch (Exception e) {
            log.error("获取文件大小失败: downloadUrl={}", downloadUrl, e);
            return 0L;
        }
    }

    /**
     * 检查文件是否存在
     * 
     * @param downloadUrl 下载URL
     * @return 是否存在
     */
    public boolean fileExists(String downloadUrl) {
        try {
            if (downloadUrl == null || !downloadUrl.startsWith(downloadBaseUrl)) {
                return false;
            }

            String relativePath = downloadUrl.substring(downloadBaseUrl.length() + 1);
            Path filePath = Paths.get(uploadBasePath, relativePath);

            return Files.exists(filePath);

        } catch (Exception e) {
            log.error("检查文件存在性失败: downloadUrl={}", downloadUrl, e);
            return false;
        }
    }

    /**
     * 清理过期文件
     * 
     * @param expireTime 过期时间
     * @return 清理的文件数量
     */
    public int cleanupExpiredFiles(LocalDateTime expireTime) {
        log.info("开始清理过期文件: expireTime={}", expireTime);

        int cleanedCount = 0;
        try {
            Path basePath = Paths.get(uploadBasePath, "pcap");
            
            if (!Files.exists(basePath)) {
                return 0;
            }

            Files.walk(basePath)
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        try {
                            return Files.getLastModifiedTime(path)
                                    .toInstant()
                                    .isBefore(java.time.Instant.from(expireTime.atZone(java.time.ZoneId.systemDefault())));
                        } catch (IOException e) {
                            return false;
                        }
                    })
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            log.debug("删除过期文件: {}", path);
                        } catch (IOException e) {
                            log.warn("删除过期文件失败: {}", path, e);
                        }
                    });

        } catch (Exception e) {
            log.error("清理过期文件失败", e);
        }

        log.info("清理过期文件完成: 清理数量={}", cleanedCount);
        return cleanedCount;
    }
}
