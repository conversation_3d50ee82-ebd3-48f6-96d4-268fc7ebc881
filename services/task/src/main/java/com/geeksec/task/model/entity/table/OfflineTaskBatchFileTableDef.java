package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 离线任务批次文件表定义
 *
 * <p>
 * 此类由 MyBatis-Flex 自动生成
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public class OfflineTaskBatchFileTableDef extends TableDef {

    /**
     * 离线任务批次文件表
     */
    public static final OfflineTaskBatchFileTableDef OFFLINE_TASK_BATCH_FILE = new OfflineTaskBatchFileTableDef();

    /**
     * 主键
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 批次ID
     */
    public final QueryColumn BATCH_ID = new QueryColumn(this, "batch_id");

    /**
     * 本地文件路径
     */
    public final QueryColumn LOCAL_PATH = new QueryColumn(this, "local_path");

    /**
     * 服务器文件路径
     */
    public final QueryColumn SERVER_PATH = new QueryColumn(this, "server_path");

    /**
     * 文件大小（字节）
     */
    public final QueryColumn FILE_SIZE = new QueryColumn(this, "file_size");

    /**
     * 文件状态 (0: 待处理, 1: 处理中, 2: 已完成, 3: 失败)
     */
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 文件类型 (1: PCAP文件, 2: 其他文件)
     */
    public final QueryColumn FILE_TYPE = new QueryColumn(this, "file_type");

    /**
     * 处理开始时间
     */
    public final QueryColumn PROCESS_START_TIME = new QueryColumn(this, "process_start_time");

    /**
     * 处理结束时间
     */
    public final QueryColumn PROCESS_END_TIME = new QueryColumn(this, "process_end_time");

    /**
     * 错误信息
     */
    public final QueryColumn ERROR_MESSAGE = new QueryColumn(this, "error_message");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 创建者用户ID
     */
    public final QueryColumn CREATED_BY = new QueryColumn(this, "created_by");

    /**
     * 更新者用户ID
     */
    public final QueryColumn UPDATED_BY = new QueryColumn(this, "updated_by");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认构造函数
     */
    public OfflineTaskBatchFileTableDef() {
        super("tb_offline_task_batch_file");
    }

    /**
     * 带别名的构造函数
     */
    public OfflineTaskBatchFileTableDef(String alias) {
        super("tb_offline_task_batch_file", alias);
    }

}