package com.geeksec.task.domain.event;

import lombok.Getter;

/**
 * PCAP下载任务处理中事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public class PcapDownloadTaskProcessingEvent extends PcapDownloadEvent {

    /**
     * 当前进度百分比
     */
    private final Integer progress;

    /**
     * 已处理文件数量
     */
    private final Integer processedFileCount;

    /**
     * 总文件数量
     */
    private final Integer totalFileCount;

    /**
     * 处理阶段描述
     */
    private final String stage;

    public PcapDownloadTaskProcessingEvent(Object source, Integer taskId, String userId, 
                                         Integer progress, Integer processedFileCount, 
                                         Integer totalFileCount, String stage) {
        super(source, taskId, userId, "PCAP下载任务处理中");
        this.progress = progress;
        this.processedFileCount = processedFileCount;
        this.totalFileCount = totalFileCount;
        this.stage = stage;
    }
}
