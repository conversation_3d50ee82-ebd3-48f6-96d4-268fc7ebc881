package com.geeksec.task.infrastructure.processor;

import com.alibaba.fastjson2.JSON;
import com.geeksec.task.application.service.PcapDownloadService;
import com.geeksec.task.infrastructure.client.SessionClient;
import com.geeksec.task.model.entity.PcapDownloadTask;
import com.geeksec.task.repository.PcapDownloadTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP下载任务异步处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PcapDownloadTaskProcessor {

    private final PcapDownloadTaskRepository pcapDownloadTaskRepository;
    private final PcapFileProcessor pcapFileProcessor;
    private final SessionClient sessionClient;
    private final FileUploadService fileUploadService;

    /**
     * 异步处理PCAP下载任务
     * 
     * @param task 下载任务
     */
    @Async("taskExecutor")
    public void processDownloadTask(PcapDownloadTask task) {
        log.info("开始异步处理PCAP下载任务: taskId={}", task.getId());

        try {
            // 更新任务状态为处理中
            updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.PROCESSING, 0, null);

            // 解析会话ID列表
            List<String> sessionIds = JSON.parseArray(task.getSessionIds(), String.class);
            log.info("解析会话ID列表: taskId={}, sessionCount={}", task.getId(), sessionIds.size());

            // 调用Session服务获取PCAP文件路径
            List<SessionClient.SessionPcapInfo> pcapFiles = getSessionPcapFiles(sessionIds);
            log.info("获取PCAP文件信息: taskId={}, fileCount={}", task.getId(), pcapFiles.size());

            if (pcapFiles.isEmpty()) {
                updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.FAILED, 0, "未找到有效的PCAP文件");
                return;
            }

            // 验证和过滤PCAP文件
            List<String> validPcapPaths = validateAndFilterPcapFiles(pcapFiles);
            log.info("验证PCAP文件: taskId={}, validCount={}", task.getId(), validPcapPaths.size());

            if (validPcapPaths.isEmpty()) {
                updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.FAILED, 0, "所有PCAP文件都无效");
                return;
            }

            // 更新任务的PCAP文件路径信息
            updateTaskPcapPaths(task.getId(), validPcapPaths);

            // 压缩PCAP文件
            String archivePath = pcapFileProcessor.processPcapDownload(task, validPcapPaths);
            log.info("PCAP文件压缩完成: taskId={}, archivePath={}", task.getId(), archivePath);

            // 上传压缩包到文件存储
            String downloadUrl = fileUploadService.uploadArchiveFile(archivePath, task);
            log.info("压缩包上传完成: taskId={}, downloadUrl={}", task.getId(), downloadUrl);

            // 更新任务状态为完成
            updateTaskCompletion(task.getId(), archivePath, downloadUrl);

            // 清理临时文件
            pcapFileProcessor.cleanupTempFiles(task);

            log.info("PCAP下载任务处理完成: taskId={}", task.getId());

        } catch (Exception e) {
            log.error("处理PCAP下载任务失败: taskId={}", task.getId(), e);
            updateTaskStatus(task.getId(), PcapDownloadTask.TaskStatus.FAILED, 0, e.getMessage());
            
            // 清理临时文件
            try {
                pcapFileProcessor.cleanupTempFiles(task);
            } catch (Exception cleanupEx) {
                log.error("清理临时文件失败: taskId={}", task.getId(), cleanupEx);
            }
        }
    }

    /**
     * 获取会话PCAP文件信息
     */
    private List<SessionClient.SessionPcapInfo> getSessionPcapFiles(List<String> sessionIds) {
        try {
            // TODO: 调用Session服务
            // return sessionClient.getSessionPcapFiles(sessionIds);
            
            // 临时模拟数据
            return sessionIds.stream()
                    .map(sessionId -> new SessionClient.SessionPcapInfo(
                        sessionId,
                        "/pcap/2024/01/01/" + sessionId + ".pcap",
                        1024L * 1024L, // 1MB
                        LocalDateTime.now().minusHours(1),
                        LocalDateTime.now(),
                        "192.168.1.100",
                        "192.168.1.200",
                        8080,
                        80,
                        6
                    ))
                    .toList();
        } catch (Exception e) {
            log.error("获取会话PCAP文件信息失败", e);
            throw new RuntimeException("获取PCAP文件信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证和过滤PCAP文件
     */
    private List<String> validateAndFilterPcapFiles(List<SessionClient.SessionPcapInfo> pcapFiles) {
        return pcapFiles.stream()
                .filter(pcapFile -> pcapFile.pcapFilePath() != null)
                .filter(pcapFile -> isValidPcapFilePath(pcapFile.pcapFilePath()))
                .map(SessionClient.SessionPcapInfo::pcapFilePath)
                .toList();
    }

    /**
     * 验证PCAP文件路径是否有效
     */
    private boolean isValidPcapFilePath(String pcapFilePath) {
        try {
            Path path = Paths.get(pcapFilePath);
            return Files.exists(path) && Files.size(path) > 0;
        } catch (Exception e) {
            log.debug("验证PCAP文件路径失败: {}", pcapFilePath, e);
            return false;
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Integer taskId, PcapDownloadTask.TaskStatus status, 
                                 Integer progress, String errorMessage) {
        try {
            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(taskId);
            updateTask.setStatus(status.getCode());
            updateTask.setProgress(progress);
            updateTask.setUpdateTime(LocalDateTime.now());

            if (status == PcapDownloadTask.TaskStatus.PROCESSING) {
                updateTask.setStartTime(LocalDateTime.now());
            }

            if (errorMessage != null) {
                updateTask.setErrorMessage(errorMessage);
            }

            pcapDownloadTaskRepository.updateById(updateTask);
            log.debug("任务状态更新: taskId={}, status={}, progress={}", taskId, status, progress);

        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}", taskId, e);
        }
    }

    /**
     * 更新任务的PCAP文件路径
     */
    private void updateTaskPcapPaths(Integer taskId, List<String> pcapPaths) {
        try {
            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(taskId);
            updateTask.setPcapFilePaths(JSON.toJSONString(pcapPaths));
            updateTask.setTotalFileCount(pcapPaths.size());
            updateTask.setUpdateTime(LocalDateTime.now());

            pcapDownloadTaskRepository.updateById(updateTask);
            log.debug("更新任务PCAP文件路径: taskId={}, fileCount={}", taskId, pcapPaths.size());

        } catch (Exception e) {
            log.error("更新任务PCAP文件路径失败: taskId={}", taskId, e);
        }
    }

    /**
     * 更新任务完成信息
     */
    private void updateTaskCompletion(Integer taskId, String archivePath, String downloadUrl) {
        try {
            // 获取压缩包文件大小
            long fileSize = java.nio.file.Files.size(java.nio.file.Paths.get(archivePath));

            PcapDownloadTask updateTask = new PcapDownloadTask();
            updateTask.setId(taskId);
            updateTask.setStatus(PcapDownloadTask.TaskStatus.COMPLETED.getCode());
            updateTask.setProgress(100);
            updateTask.setArchiveFilePath(archivePath);
            updateTask.setArchiveFileSize(fileSize);
            updateTask.setDownloadUrl(downloadUrl);
            updateTask.setCompleteTime(LocalDateTime.now());
            updateTask.setUpdateTime(LocalDateTime.now());

            pcapDownloadTaskRepository.updateById(updateTask);
            log.info("任务完成信息更新: taskId={}, fileSize={}, downloadUrl={}", 
                    taskId, fileSize, downloadUrl);

        } catch (Exception e) {
            log.error("更新任务完成信息失败: taskId={}", taskId, e);
        }
    }

    /**
     * Session客户端（临时接口定义）
     */
    public interface SessionClient {
        record SessionPcapInfo(
            String sessionId,
            String pcapFilePath,
            Long fileSize,
            LocalDateTime sessionStartTime,
            LocalDateTime sessionEndTime,
            String srcIp,
            String dstIp,
            Integer srcPort,
            Integer dstPort,
            Integer protocol
        ) {}
    }

    /**
     * 文件上传服务（临时接口定义）
     */
    public interface FileUploadService {
        String uploadArchiveFile(String archivePath, PcapDownloadTask task);
    }
}
