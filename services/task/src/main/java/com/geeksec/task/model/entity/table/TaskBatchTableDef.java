package com.geeksec.task.model.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 任务批次表定义
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 * @generated by MyBatis-Flex
 */
public class TaskBatchTableDef extends TableDef {

    /**
     * 任务批次表定义
     */
    public static final TaskBatchTableDef TASK_BATCH = new TaskBatchTableDef();

    /**
     * 自增批次ID
     */
    public final QueryColumn BATCH_ID = new QueryColumn(this, "batch_id");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 任务类型 (1: 在线任务, 2: 离线任务)
     */
    public final QueryColumn TASK_TYPE = new QueryColumn(this, "task_type");

    /**
     * 批次类型 (1: 服务器数据, 2: 数据上传)
     */
    public final QueryColumn BATCH_TYPE = new QueryColumn(this, "batch_type");

    /**
     * 批次描述
     */
    public final QueryColumn BATCH_REMARK = new QueryColumn(this, "batch_remark");

    /**
     * 全流量留存状态 (ON: 启用, OFF: 停用)
     */
    public final QueryColumn FULLFLOW_STATE = new QueryColumn(this, "fullflow_state");

    /**
     * 流量日志留存状态 (ON: 启用, OFF: 停用)
     */
    public final QueryColumn FLOWLOG_STATE = new QueryColumn(this, "flowlog_state");

    /**
     * 导入的数据类型 (1: pcap, 2: pb, 3: 探针数据)
     */
    public final QueryColumn DATA_TYPE = new QueryColumn(this, "data_type");

    /**
     * MAC日志留存状态 (ON: 启用, OFF: 停用)
     */
    public final QueryColumn TOPOLOGY_STATE = new QueryColumn(this, "topology_state");

    /**
     * 导入开始时间
     */
    public final QueryColumn BEGIN_TIME = new QueryColumn(this, "begin_time");

    /**
     * 导入结束时间
     */
    public final QueryColumn END_TIME = new QueryColumn(this, "end_time");

    /**
     * 数据开始时间
     */
    public final QueryColumn DATA_BEGIN_TIME = new QueryColumn(this, "data_begin_time");

    /**
     * 数据结束时间
     */
    public final QueryColumn DATA_END_TIME = new QueryColumn(this, "data_end_time");

    /**
     * 导入数据量（字节）
     */
    public final QueryColumn BATCH_BYTES = new QueryColumn(this, "batch_bytes");

    /**
     * 导入会话量
     */
    public final QueryColumn BATCH_SESSION = new QueryColumn(this, "batch_session");

    /**
     * 批次的告警量
     */
    public final QueryColumn BATCH_ALARM = new QueryColumn(this, "batch_alarm");

    /**
     * 高危目标数量
     */
    public final QueryColumn IMPORTRARNT_TARGET = new QueryColumn(this, "importrarnt_target");

    /**
     * 过滤数据量
     */
    public final QueryColumn FILTER_DATA_TOTAL = new QueryColumn(this, "filter_data_total");

    /**
     * 采集规则命中数据量
     */
    public final QueryColumn RULE_HITS_DATA_TOTAL = new QueryColumn(this, "rule_hits_data_total");

    /**
     * 白名单过滤量
     */
    public final QueryColumn WHITELIST_FILTER_TOTAL = new QueryColumn(this, "whitelist_filter_total");

    /**
     * 任务状态 (1: 等待导入, 2: 正在导入, 3: 导入完成, 4: 导入失败)
     */
    public final QueryColumn BATCH_STATUS = new QueryColumn(this, "batch_status");

    /**
     * 导入进度
     */
    public final QueryColumn BATCH_PROGRESS = new QueryColumn(this, "batch_progress");

    /**
     * 当前任务批次数据路径
     */
    public final QueryColumn BATCH_DIR = new QueryColumn(this, "batch_dir");

    /**
     * 报告路径
     */
    public final QueryColumn REPORT_PATH = new QueryColumn(this, "report_path");

    /**
     * 筛选条件
     */
    public final QueryColumn SCREENING_CONDITIONS = new QueryColumn(this, "screening_conditions");

    /**
     * 平均每秒流量
     */
    public final QueryColumn AVG_BYTE_PT_PS = new QueryColumn(this, "avg_byte_pt_ps");

    /**
     * 最大每秒流量
     */
    public final QueryColumn MAX_BYTE_PT_PS = new QueryColumn(this, "max_byte_pt_ps");

    /**
     * 地址
     */
    public final QueryColumn ADDR = new QueryColumn(this, "addr");

    /**
     * 任务更新
     */
    public final QueryColumn TASK_UPDATE = new QueryColumn(this, "task_update");

    /**
     * 全流量应记录定义
     */
    public final QueryColumn FULL_FLOW_SHOULD_LOG_DEF = new QueryColumn(this, "full_flow_should_log_def");

    /**
     * 解析协议应记录定义
     */
    public final QueryColumn PARSE_PROTO_SHOULD_LOG_DEF = new QueryColumn(this, "parse_proto_should_log_def");

    /**
     * 状态
     */
    public final QueryColumn STATE = new QueryColumn(this, "state");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 创建人
     */
    public final QueryColumn CREATED_BY = new QueryColumn(this, "created_by");

    /**
     * 更新人
     */
    public final QueryColumn UPDATED_BY = new QueryColumn(this, "updated_by");

    /**
     * 所有字段
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认构造函数
     */
    public TaskBatchTableDef() {
        super("tb_task_batch", "tb_task_batch");
    }

    /**
     * 带别名的构造函数
     */
    public TaskBatchTableDef(String alias) {
        super("tb_task_batch", alias);
    }

    /**
     * 获取表名
     */
    @Override
    public String getTableName() {
        return "tb_task_batch";
    }
}