package com.geeksec.task.domain.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * PCAP下载任务事件基类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public abstract class PcapDownloadEvent extends ApplicationEvent {

    /**
     * 任务ID
     */
    private final Integer taskId;

    /**
     * 用户ID
     */
    private final String userId;

    /**
     * 事件时间
     */
    private final LocalDateTime eventTime;

    /**
     * 事件描述
     */
    private final String description;

    public PcapDownloadEvent(Object source, Integer taskId, String userId, String description) {
        super(source);
        this.taskId = taskId;
        this.userId = userId;
        this.description = description;
        this.eventTime = LocalDateTime.now();
    }
}
