package com.geeksec.metadata.repository;

import com.geeksec.metadata.model.entity.EntityLabel;
import com.geeksec.metadata.model.enums.LabelTargetType;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 统一标签关联Repository接口
 * 对应 entity_labels 表
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface EntityLabelRepository extends BaseMapper<EntityLabel> {

    /**
     * 根据实体类型和实体ID查询标签关联
     * 
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 标签关联列表
     */
    default List<EntityLabel> selectByEntity(LabelTargetType entityType, String entityId) {
        return selectListByQuery(
            QueryWrapper.create()
                .eq("entity_type", entityType)
                .eq("entity_id", entityId)
                .orderBy("created_at", true)
        );
    }

    /**
     * 根据标签ID查询实体关联
     * 
     * @param labelId 标签ID
     * @return 实体关联列表
     */
    default List<EntityLabel> selectByLabelId(Integer labelId) {
        return selectListByQuery(
            QueryWrapper.create()
                .eq("label_id", labelId)
                .orderBy("created_at", true)
        );
    }

    /**
     * 根据实体类型查询标签关联
     * 
     * @param entityType 实体类型
     * @return 标签关联列表
     */
    default List<EntityLabel> selectByEntityType(LabelTargetType entityType) {
        return selectListByQuery(
            QueryWrapper.create()
                .eq("entity_type", entityType)
                .orderBy("created_at", true)
        );
    }

    /**
     * 检查实体与标签的关联是否存在
     * 
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param labelId 标签ID
     * @return 是否存在关联
     */
    default boolean existsEntityLabel(LabelTargetType entityType, String entityId, Integer labelId) {
        return selectCountByQuery(
            QueryWrapper.create()
                .eq("entity_type", entityType)
                .eq("entity_id", entityId)
                .eq("label_id", labelId)
        ) > 0;
    }

    /**
     * 删除实体与标签的关联
     * 
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @param labelId 标签ID
     * @return 删除的记录数
     */
    default int deleteEntityLabel(LabelTargetType entityType, String entityId, Integer labelId) {
        return deleteByQuery(
            QueryWrapper.create()
                .eq("entity_type", entityType)
                .eq("entity_id", entityId)
                .eq("label_id", labelId)
        );
    }

    /**
     * 删除实体的所有标签关联
     * 
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 删除的记录数
     */
    default int deleteByEntity(LabelTargetType entityType, String entityId) {
        return deleteByQuery(
            QueryWrapper.create()
                .eq("entity_type", entityType)
                .eq("entity_id", entityId)
        );
    }

    /**
     * 删除标签的所有实体关联
     * 
     * @param labelId 标签ID
     * @return 删除的记录数
     */
    default int deleteByLabelId(Integer labelId) {
        return deleteByQuery(
            QueryWrapper.create()
                .eq("label_id", labelId)
        );
    }

    /**
     * 统计实体的标签数量
     * 
     * @param entityType 实体类型
     * @param entityId 实体ID
     * @return 标签数量
     */
    default Long countByEntity(LabelTargetType entityType, String entityId) {
        return selectCountByQuery(
            QueryWrapper.create()
                .eq("entity_type", entityType)
                .eq("entity_id", entityId)
        );
    }

    /**
     * 统计标签关联的实体数量
     * 
     * @param labelId 标签ID
     * @return 实体数量
     */
    default Long countByLabelId(Integer labelId) {
        return selectCountByQuery(
            QueryWrapper.create()
                .eq("label_id", labelId)
        );
    }
}
