package com.geeksec.metadata.model.entity;

import com.geeksec.metadata.model.enums.LabelTargetType;
import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一标签关联实体类
 * 对应 entity_labels 表
 * 支持所有类型实体与标签的关联关系
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
@Table("entity_labels")
public class EntityLabel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联唯一标识
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 实体类型（IP、CERTIFICATE、SESSION、DOMAIN、APPLICATION、FINGERPRINT、MAC、PORT）
     */
    @Column("entity_type")
    private LabelTargetType entityType;

    /**
     * 实体标识符（如IP地址、证书哈希、会话ID等）
     */
    @Column("entity_id")
    private String entityId;

    /**
     * 标签ID，引用labels表
     */
    @Column("label_id")
    private Integer labelId;

    /**
     * 创建者用户ID
     */
    @Column("created_by")
    private Integer createdBy;

    /**
     * 更新者用户ID
     */
    @Column("updated_by")
    private Integer updatedBy;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;
}
