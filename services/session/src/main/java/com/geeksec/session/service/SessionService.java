package com.geeksec.session.service;

import com.geeksec.session.exception.SessionNotFoundException;
import com.geeksec.session.model.dto.SessionListResponse;
import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.mybatisflex.core.paginate.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话服务接口 - 基于Doris只读查询
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SessionService {

    /**
     * 根据ID获取会话详情
     *
     * @param id 会话ID
     * @return 会话实体
     * @throws SessionNotFoundException 当会话不存在时抛出异常
     */
    Session getSessionById(String id) throws SessionNotFoundException;

    /**
     * 分页查询会话列表
     *
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<SessionListResponse> querySessionList(SessionQueryRequest queryRequest);

    /**
     * 统计会话数量
     *
     * @param queryRequest 查询条件
     * @return 会话数量
     */
    long countSessions(SessionQueryRequest queryRequest);

    /**
     * 根据多个ID批量查询会话
     *
     * @param sessionIds 会话ID列表
     * @return 会话列表
     */
    List<Session> getSessionsByIds(List<String> sessionIds);

    /**
     * 查询会话统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组字段
     * @return 统计结果
     */
    List<Map<String, Object>> getSessionStatistics(LocalDateTime startTime, LocalDateTime endTime, String groupBy);

    /**
     * 查询会话聚合信息
     *
     * @param queryRequest 查询条件
     * @return 聚合结果
     */
    Page<Map<String, Object>> getSessionAggregation(SessionQueryRequest queryRequest);

    /**
     * 查询热门源IP统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @return 源IP统计列表
     */
    List<Map<String, Object>> getTopSourceIps(LocalDateTime startTime, LocalDateTime endTime, int limit);

    /**
     * 查询热门目标IP统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @return 目标IP统计列表
     */
    List<Map<String, Object>> getTopDestinationIps(LocalDateTime startTime, LocalDateTime endTime, int limit);

    /**
     * 查询协议分布统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 协议分布统计
     */
    List<Map<String, Object>> getProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询应用分布统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 应用分布统计
     */
    List<Map<String, Object>> getApplicationDistribution(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询时间范围内的会话趋势
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔(分钟)
     * @return 会话趋势数据
     */
    List<Map<String, Object>> getSessionTrend(LocalDateTime startTime, LocalDateTime endTime, int interval);

    /**
     * 根据关键字搜索会话
     *
     * @param keyword 关键字
     * @param page 分页参数
     * @return 搜索结果
     */
    Page<SessionListResponse> searchSessions(String keyword, Page<SessionListResponse> page);

    /**
     * 检查会话是否存在
     *
     * @param id 会话ID
     * @return 是否存在
     */
    boolean existsById(String id);

    /**
     * 根据会话ID列表查询PCAP文件信息
     *
     * @param sessionIds 会话ID列表
     * @return PCAP文件信息列表
     */
    List<SessionPcapInfo> getSessionPcapFiles(List<String> sessionIds);

    /**
     * 验证会话ID列表的有效性
     *
     * @param sessionIds 会话ID列表
     * @return 验证结果
     */
    SessionValidationResult validateSessionIds(List<String> sessionIds);

    /**
     * 会话PCAP文件信息
     */
    record SessionPcapInfo(
        String sessionId,
        String pcapFilePath,
        Long fileSize,
        LocalDateTime sessionStartTime,
        LocalDateTime sessionEndTime,
        String srcIp,
        String dstIp,
        Integer srcPort,
        Integer dstPort,
        Integer protocol
    ) {}

    /**
     * 会话验证结果
     */
    record SessionValidationResult(
        List<String> validSessionIds,
        List<String> invalidSessionIds,
        int totalCount,
        int validCount,
        int invalidCount
    ) {}

    /**
     * 下载单个会话的PCAP文件
     *
     * @param sessionId 会话ID
     * @param response HTTP响应对象
     */
    void downloadSessionPcap(String sessionId, jakarta.servlet.http.HttpServletResponse response);

    /**
     * 统一的PCAP下载接口
     * 根据业务复杂度自动选择同步或异步处理
     *
     * @param request PCAP下载请求
     * @return 下载响应
     */
    PcapDownloadResponse downloadSessionsPcap(PcapDownloadRequest request);

    /**
     * 查询PCAP下载任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    PcapDownloadStatus getDownloadTaskStatus(String taskId);

    /**
     * 检查PCAP文件状态
     *
     * @param sessionIds 会话ID列表
     * @return 文件状态列表
     */
    List<PcapFileStatus> checkPcapFiles(List<String> sessionIds);

    /**
     * 批量下载请求
     */
    record BatchDownloadRequest(
        List<String> sessionIds,
        String downloadName,
        String format
    ) {}

    /**
     * 批量下载响应
     */
    record BatchDownloadResponse(
        String downloadId,
        String downloadUrl,
        int totalFiles,
        long totalSize,
        String status,
        LocalDateTime expireTime
    ) {}

    /**
     * PCAP文件状态
     */
    record PcapFileStatus(
        String sessionId,
        boolean exists,
        long fileSize,
        String filePath,
        String errorMessage
    ) {}

    /**
     * PCAP下载请求
     */
    record PcapDownloadRequest(
        List<String> sessionIds,
        String downloadName,
        String requestSource,
        String userId,
        java.util.Map<String, Object> metadata
    ) {}

    /**
     * PCAP下载响应
     */
    record PcapDownloadResponse(
        String downloadId,
        String status,
        String downloadUrl,
        String taskId,
        int totalSessions,
        long estimatedSize,
        String message,
        LocalDateTime createTime,
        LocalDateTime expireTime
    ) {}

    /**
     * PCAP下载状态
     */
    record PcapDownloadStatus(
        String downloadId,
        String taskId,
        String status,
        int progress,
        String downloadUrl,
        long fileSize,
        String errorMessage,
        LocalDateTime createTime,
        LocalDateTime completeTime
    ) {}
}
