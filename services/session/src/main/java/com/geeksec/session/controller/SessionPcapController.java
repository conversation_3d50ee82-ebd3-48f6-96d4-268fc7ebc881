package com.geeksec.session.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.session.service.SessionService;
import com.geeksec.session.service.SessionService.SessionPcapInfo;
import com.geeksec.session.service.SessionService.SessionValidationResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 会话PCAP文件控制器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/session/pcap")
@RequiredArgsConstructor
@Validated
@Tag(name = "会话PCAP管理", description = "会话PCAP文件相关操作")
public class SessionPcapController {

    private final SessionService sessionService;

    /**
     * 根据会话ID列表查询PCAP文件信息
     */
    @PostMapping("/files")
    @Operation(summary = "查询PCAP文件信息", description = "根据会话ID列表查询对应的PCAP文件信息")
    public ApiResponse<List<SessionPcapInfo>> getSessionPcapFiles(
            @Parameter(description = "会话ID列表", required = true)
            @RequestBody List<String> sessionIds) {
        try {
            if (sessionIds == null || sessionIds.isEmpty()) {
                return ApiResponse.badRequest("会话ID列表不能为空");
            }

            if (sessionIds.size() > 1000) {
                return ApiResponse.badRequest("单次查询会话数量不能超过1000个");
            }

            List<SessionPcapInfo> pcapFiles = sessionService.getSessionPcapFiles(sessionIds);
            return ApiResponse.success(pcapFiles);
        } catch (Exception e) {
            log.error("查询PCAP文件信息失败", e);
            return ApiResponse.error("查询PCAP文件信息失败：" + e.getMessage());
        }
    }

    /**
     * 验证会话ID列表的有效性
     */
    @PostMapping("/validate")
    @Operation(summary = "验证会话ID", description = "验证会话ID列表的有效性")
    public ApiResponse<SessionValidationResult> validateSessionIds(
            @Parameter(description = "会话ID列表", required = true)
            @RequestBody List<String> sessionIds) {
        try {
            if (sessionIds == null || sessionIds.isEmpty()) {
                return ApiResponse.badRequest("会话ID列表不能为空");
            }

            if (sessionIds.size() > 1000) {
                return ApiResponse.badRequest("单次验证会话数量不能超过1000个");
            }

            SessionValidationResult result = sessionService.validateSessionIds(sessionIds);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("验证会话ID失败", e);
            return ApiResponse.error("验证会话ID失败：" + e.getMessage());
        }
    }

    /**
     * 批量查询会话基本信息
     */
    @PostMapping("/batch")
    @Operation(summary = "批量查询会话信息", description = "根据会话ID列表批量查询会话基本信息")
    public ApiResponse<List<SessionBasicInfo>> getSessionBasicInfo(
            @Parameter(description = "会话ID列表", required = true)
            @RequestBody List<String> sessionIds) {
        try {
            if (sessionIds == null || sessionIds.isEmpty()) {
                return ApiResponse.badRequest("会话ID列表不能为空");
            }

            if (sessionIds.size() > 1000) {
                return ApiResponse.badRequest("单次查询会话数量不能超过1000个");
            }

            var sessions = sessionService.getSessionsByIds(sessionIds);
            var basicInfoList = sessions.stream()
                    .map(session -> new SessionBasicInfo(
                        session.getSessionId(),
                        session.getSrcIp(),
                        session.getDstIp(),
                        session.getSrcPort(),
                        session.getDstPort(),
                        session.getProtocol(),
                        session.getSessionStartTime(),
                        session.getSessionEndTime()
                    ))
                    .toList();

            return ApiResponse.success(basicInfoList);
        } catch (Exception e) {
            log.error("批量查询会话信息失败", e);
            return ApiResponse.error("批量查询会话信息失败：" + e.getMessage());
        }
    }

    /**
     * 下载单个会话的PCAP文件
     */
    @GetMapping("/download/{sessionId}")
    @Operation(summary = "下载PCAP文件", description = "下载指定会话的PCAP文件")
    public void downloadSessionPcap(
            @Parameter(description = "会话ID", required = true)
            @PathVariable String sessionId,
            HttpServletResponse response) {
        try {
            sessionService.downloadSessionPcap(sessionId, response);
        } catch (Exception e) {
            log.error("下载PCAP文件失败: sessionId={}", sessionId, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 统一的PCAP下载接口（支持单个或多个sessionId）
     */
    @PostMapping("/download")
    @Operation(summary = "下载PCAP文件", description = "根据会话ID列表下载对应的PCAP文件，自动合并为单个文件")
    public ApiResponse<PcapDownloadResponse> downloadSessionsPcap(
            @Parameter(description = "PCAP下载请求", required = true)
            @RequestBody PcapDownloadRequest request) {
        try {
            if (request.sessionIds() == null || request.sessionIds().isEmpty()) {
                return ApiResponse.badRequest("会话ID列表不能为空");
            }

            if (request.sessionIds().size() > 1000) {
                return ApiResponse.badRequest("单次下载会话数量不能超过1000个");
            }

            PcapDownloadResponse response = sessionService.downloadSessionsPcap(request);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("下载PCAP文件失败", e);
            return ApiResponse.error("下载失败：" + e.getMessage());
        }
    }

    /**
     * 查询PCAP下载任务状态
     */
    @GetMapping("/download/{taskId}/status")
    @Operation(summary = "查询下载任务状态", description = "查询PCAP下载任务的处理状态")
    public ApiResponse<PcapDownloadStatus> getDownloadTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        try {
            PcapDownloadStatus status = sessionService.getDownloadTaskStatus(taskId);
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("查询下载任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("查询任务状态失败：" + e.getMessage());
        }
    }

    /**
     * 检查PCAP文件是否存在
     */
    @PostMapping("/check")
    @Operation(summary = "检查PCAP文件", description = "检查指定会话的PCAP文件是否存在")
    public ApiResponse<List<PcapFileStatus>> checkPcapFiles(
            @Parameter(description = "会话ID列表", required = true)
            @RequestBody List<String> sessionIds) {
        try {
            List<PcapFileStatus> statuses = sessionService.checkPcapFiles(sessionIds);
            return ApiResponse.success(statuses);
        } catch (Exception e) {
            log.error("检查PCAP文件失败", e);
            return ApiResponse.error("检查文件失败：" + e.getMessage());
        }
    }

    /**
     * 会话基本信息记录
     */
    public record SessionBasicInfo(
        String sessionId,
        String srcIp,
        String dstIp,
        Integer srcPort,
        Integer dstPort,
        Integer protocol,
        java.time.LocalDateTime sessionStartTime,
        java.time.LocalDateTime sessionEndTime
    ) {}

    /**
     * 批量下载请求
     */
    public record BatchDownloadRequest(
        List<String> sessionIds,
        String downloadName,
        String format // "zip" or "tar.gz"
    ) {}

    /**
     * 批量下载响应
     */
    public record BatchDownloadResponse(
        String downloadId,
        String downloadUrl,
        int totalFiles,
        long totalSize,
        String status,
        java.time.LocalDateTime expireTime
    ) {}

    /**
     * PCAP文件状态
     */
    public record PcapFileStatus(
        String sessionId,
        boolean exists,
        long fileSize,
        String filePath,
        String errorMessage
    ) {}

    /**
     * PCAP下载请求
     */
    public record PcapDownloadRequest(
        List<String> sessionIds,
        String downloadName,
        String requestSource, // "ALARM", "MANUAL", etc.
        String userId,
        Map<String, Object> metadata // 额外的元数据，如告警类型、时间等
    ) {}

    /**
     * PCAP下载响应
     */
    public record PcapDownloadResponse(
        String downloadId,
        String status, // "PROCESSING", "COMPLETED", "FAILED"
        String downloadUrl,
        String taskId, // 如果是异步处理，返回任务ID
        int totalSessions,
        long estimatedSize,
        String message,
        java.time.LocalDateTime createTime,
        java.time.LocalDateTime expireTime
    ) {}

    /**
     * PCAP下载状态
     */
    public record PcapDownloadStatus(
        String downloadId,
        String taskId,
        String status,
        int progress, // 0-100
        String downloadUrl,
        long fileSize,
        String errorMessage,
        java.time.LocalDateTime createTime,
        java.time.LocalDateTime completeTime
    ) {}
}
