package com.geeksec.session.service.impl;

import com.geeksec.session.exception.SessionNotFoundException;
import com.geeksec.session.model.dto.SessionListResponse;
import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.session.infrastructure.client.TaskClient;
import com.geeksec.session.repository.SessionRepository;
import com.geeksec.session.service.SessionService;
import com.mybatisflex.core.paginate.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话服务实现类 - 基于Doris只读查询
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SessionServiceImpl implements SessionService {

    private final SessionRepository sessionRepository;
    private final TaskClient taskClient;

    @Override
    public Session getSessionById(String id) throws SessionNotFoundException {
        log.debug("根据ID查询会话详情: {}", id);

        Session session = sessionRepository.getSessionById(id);
        if (session == null) {
            throw SessionNotFoundException.withId(id);
        }
        return session;
    }

    @Override
    public Page<SessionListResponse> querySessionList(SessionQueryRequest queryRequest) {
        log.debug("分页查询会话列表, 参数: {}", queryRequest);

        return sessionRepository.querySessionList(queryRequest);
    }

    @Override
    public long countSessions(SessionQueryRequest queryRequest) {
        log.debug("统计会话数量, 参数: {}", queryRequest);

        return sessionRepository.countSessions(queryRequest);
    }

    @Override
    public List<Session> getSessionsByIds(List<String> sessionIds) {
        log.debug("批量查询会话, IDs: {}", sessionIds);

        if (sessionIds == null || sessionIds.isEmpty()) {
            return List.of();
        }

        return sessionRepository.getSessionsByIds(sessionIds);
    }

    @Override
    public List<Map<String, Object>> getSessionStatistics(LocalDateTime startTime, LocalDateTime endTime, String groupBy) {
        log.debug("查询会话统计信息, 开始时间: {}, 结束时间: {}, 分组字段: {}", startTime, endTime, groupBy);

        return sessionRepository.getSessionStatistics(startTime, endTime, groupBy);
    }

    @Override
    public Page<Map<String, Object>> getSessionAggregation(SessionQueryRequest queryRequest) {
        log.debug("查询会话聚合信息, 参数: {}", queryRequest);

        return sessionRepository.getSessionAggregation(queryRequest);
    }

    @Override
    public List<Map<String, Object>> getTopSourceIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        log.debug("查询热门源IP统计, 开始时间: {}, 结束时间: {}, 限制数量: {}", startTime, endTime, limit);

        return sessionRepository.getTopSourceIps(startTime, endTime, limit);
    }

    @Override
    public List<Map<String, Object>> getTopDestinationIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        log.debug("查询热门目标IP统计, 开始时间: {}, 结束时间: {}, 限制数量: {}", startTime, endTime, limit);

        return sessionRepository.getTopDestinationIps(startTime, endTime, limit);
    }

    @Override
    public List<Map<String, Object>> getProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询协议分布统计, 开始时间: {}, 结束时间: {}", startTime, endTime);

        return sessionRepository.getProtocolDistribution(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getApplicationDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询应用分布统计, 开始时间: {}, 结束时间: {}", startTime, endTime);

        return sessionRepository.getApplicationDistribution(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getSessionTrend(LocalDateTime startTime, LocalDateTime endTime, int interval) {
        log.debug("查询会话趋势, 开始时间: {}, 结束时间: {}, 间隔: {}分钟", startTime, endTime, interval);

        return sessionRepository.getSessionTrend(startTime, endTime, interval);
    }

    @Override
    public Page<SessionListResponse> searchSessions(String keyword, Page<SessionListResponse> page) {
        log.debug("搜索会话, 关键字: {}", keyword);

        return sessionRepository.searchSessions(keyword, page);
    }

    @Override
    public boolean existsById(String id) {
        log.debug("检查会话是否存在, ID: {}", id);

        try {
            Session session = sessionRepository.getSessionById(id);
            return session != null;
        } catch (Exception e) {
            log.debug("会话不存在, ID: {}", id);
            return false;
        }
    }

    @Override
    public List<SessionPcapInfo> getSessionPcapFiles(List<String> sessionIds) {
        log.debug("查询会话PCAP文件信息, 会话数量: {}", sessionIds.size());

        if (sessionIds == null || sessionIds.isEmpty()) {
            return List.of();
        }

        // 批量查询会话信息
        List<Session> sessions = sessionRepository.getSessionsByIds(sessionIds);

        return sessions.stream()
                .map(this::convertToSessionPcapInfo)
                .filter(info -> info.pcapFilePath() != null && !info.pcapFilePath().isEmpty())
                .toList();
    }

    @Override
    public SessionValidationResult validateSessionIds(List<String> sessionIds) {
        log.debug("验证会话ID列表, 数量: {}", sessionIds.size());

        if (sessionIds == null || sessionIds.isEmpty()) {
            return new SessionValidationResult(List.of(), List.of(), 0, 0, 0);
        }

        // 查询存在的会话ID
        List<String> existingSessionIds = sessionRepository.getExistingSessionIds(sessionIds);

        // 计算无效的会话ID
        List<String> invalidSessionIds = sessionIds.stream()
                .filter(id -> !existingSessionIds.contains(id))
                .toList();

        return new SessionValidationResult(
            existingSessionIds,
            invalidSessionIds,
            sessionIds.size(),
            existingSessionIds.size(),
            invalidSessionIds.size()
        );
    }

    /**
     * 转换会话实体为PCAP文件信息
     */
    private SessionPcapInfo convertToSessionPcapInfo(Session session) {
        return new SessionPcapInfo(
            session.getSessionId(),
            buildPcapFilePath(session), // 构建PCAP文件路径
            calculateFileSize(session), // 计算文件大小
            session.getSessionStartTime(),
            session.getSessionEndTime(),
            session.getSrcIp(),
            session.getDstIp(),
            session.getSrcPort(),
            session.getDstPort(),
            session.getProtocol()
        );
    }

    /**
     * 构建PCAP文件路径
     */
    private String buildPcapFilePath(Session session) {
        // 根据会话信息构建PCAP文件路径
        // 这里需要根据实际的文件存储规则来实现
        if (session.getSessionStartTime() != null) {
            String dateStr = session.getSessionStartTime().toLocalDate().toString();
            return String.format("/pcap/%s/%s.pcap", dateStr, session.getSessionId());
        }
        return null;
    }

    /**
     * 计算文件大小
     */
    private Long calculateFileSize(Session session) {
        // 根据会话的数据包信息估算文件大小
        // 这里可以根据实际需求实现更精确的计算
        if (session.getSrcTotalBytes() != null && session.getDstTotalBytes() != null) {
            return session.getSrcTotalBytes() + session.getDstTotalBytes();
        }
        return 0L;
    }

    @Override
    public void downloadSessionPcap(String sessionId, jakarta.servlet.http.HttpServletResponse response) {
        log.info("下载会话PCAP文件: sessionId={}", sessionId);

        try {
            // 查询会话信息
            Session session = sessionRepository.getSessionById(sessionId);
            if (session == null) {
                response.setStatus(jakarta.servlet.http.HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 构建PCAP文件路径
            String pcapFilePath = buildPcapFilePath(session);
            if (pcapFilePath == null) {
                response.setStatus(jakarta.servlet.http.HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 检查文件是否存在
            java.nio.file.Path filePath = java.nio.file.Paths.get("/data/pcap", pcapFilePath);
            if (!java.nio.file.Files.exists(filePath)) {
                response.setStatus(jakarta.servlet.http.HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition",
                "attachment; filename=\"" + sessionId + ".pcap\"");
            response.setContentLengthLong(java.nio.file.Files.size(filePath));

            // 传输文件
            try (java.io.InputStream inputStream = java.nio.file.Files.newInputStream(filePath);
                 java.io.OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

            log.info("PCAP文件下载完成: sessionId={}, fileSize={}",
                    sessionId, java.nio.file.Files.size(filePath));

        } catch (Exception e) {
            log.error("下载PCAP文件失败: sessionId={}", sessionId, e);
            response.setStatus(jakarta.servlet.http.HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public PcapDownloadResponse downloadSessionsPcap(PcapDownloadRequest request) {
        log.info("PCAP异步下载: sessionCount={}, source={}",
                request.sessionIds().size(), request.requestSource());

        try {
            // 1. 验证会话ID
            SessionValidationResult validation = validateSessionIds(request.sessionIds());
            if (validation.validCount() == 0) {
                throw new RuntimeException("没有找到有效的会话ID");
            }

            // 2. 获取PCAP文件信息
            List<SessionPcapInfo> pcapFiles = getSessionPcapFiles(validation.validSessionIds());
            if (pcapFiles.isEmpty()) {
                throw new RuntimeException("没有找到有效的PCAP文件");
            }

            // 3. 统一创建异步任务
            String downloadId = java.util.UUID.randomUUID().toString();
            String taskId = createAsyncDownloadTask(downloadId, pcapFiles, request);

            return new PcapDownloadResponse(
                downloadId,
                "PROCESSING",
                null, // 异步处理时下载链接稍后生成
                taskId,
                pcapFiles.size(),
                pcapFiles.stream().mapToLong(SessionPcapInfo::fileSize).sum(),
                "正在后台处理PCAP文件合并",
                java.time.LocalDateTime.now(),
                java.time.LocalDateTime.now().plusHours(24)
            );

        } catch (Exception e) {
            log.error("PCAP下载失败", e);
            throw new RuntimeException("PCAP下载失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PcapDownloadStatus getDownloadTaskStatus(String taskId) {
        log.debug("查询下载任务状态: taskId={}", taskId);

        try {
            // 调用Task模块查询任务状态
            Integer taskIdInt = Integer.parseInt(taskId.replace("task_", ""));
            ApiResponse<TaskClient.PcapDownloadTaskResult> response =
                taskClient.getDownloadTaskStatus(taskIdInt);

            if (!response.isSuccess()) {
                throw new RuntimeException("查询任务状态失败: " + response.getMessage());
            }

            TaskClient.PcapDownloadTaskResult taskResult = response.getData();

            return new PcapDownloadStatus(
                taskId,
                taskResult.taskId().toString(),
                taskResult.status(),
                taskResult.progress() != null ? taskResult.progress() : 0,
                taskResult.downloadUrl(),
                taskResult.archiveFileSize() != null ? taskResult.archiveFileSize() : 0L,
                taskResult.errorMessage(),
                taskResult.createTime(),
                taskResult.completeTime()
            );

        } catch (Exception e) {
            log.error("查询下载任务状态失败: taskId={}", taskId, e);
            throw new RuntimeException("查询任务状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<PcapFileStatus> checkPcapFiles(List<String> sessionIds) {
        log.debug("检查PCAP文件状态: sessionCount={}", sessionIds.size());

        return sessionIds.stream()
                .map(this::checkSinglePcapFile)
                .toList();
    }

    /**
     * 检查单个PCAP文件状态
     */
    private PcapFileStatus checkSinglePcapFile(String sessionId) {
        try {
            Session session = sessionRepository.getSessionById(sessionId);
            if (session == null) {
                return new PcapFileStatus(sessionId, false, 0L, null, "会话不存在");
            }

            String pcapFilePath = buildPcapFilePath(session);
            if (pcapFilePath == null) {
                return new PcapFileStatus(sessionId, false, 0L, null, "无法构建文件路径");
            }

            java.nio.file.Path filePath = java.nio.file.Paths.get("/data/pcap", pcapFilePath);
            if (!java.nio.file.Files.exists(filePath)) {
                return new PcapFileStatus(sessionId, false, 0L, pcapFilePath, "文件不存在");
            }

            long fileSize = java.nio.file.Files.size(filePath);
            return new PcapFileStatus(sessionId, true, fileSize, pcapFilePath, null);

        } catch (Exception e) {
            log.error("检查PCAP文件失败: sessionId={}", sessionId, e);
            return new PcapFileStatus(sessionId, false, 0L, null, e.getMessage());
        }
    }



    /**
     * 创建异步下载任务
     */
    private String createAsyncDownloadTask(String downloadId,
                                          List<SessionPcapInfo> pcapFiles,
                                          PcapDownloadRequest request) {
        log.info("创建异步下载任务: downloadId={}, fileCount={}", downloadId, pcapFiles.size());

        try {
            // 构建Task模块需要的请求参数
            TaskClient.PcapDownloadRequest taskRequest = new TaskClient.PcapDownloadRequest(
                request.userId(),
                request.metadata().get("alarmType") != null ?
                    request.metadata().get("alarmType").toString() : "UNKNOWN",
                request.metadata().get("alarmTime") != null ?
                    (Long) request.metadata().get("alarmTime") : System.currentTimeMillis(),
                request.sessionIds(),
                String.format("Session模块PCAP合并任务 - %s", downloadId)
            );

            // 调用Task模块创建任务
            ApiResponse<TaskClient.PcapDownloadTaskResult> response =
                taskClient.createPcapDownloadTask(taskRequest);

            if (!response.isSuccess()) {
                throw new RuntimeException("创建Task任务失败: " + response.getMessage());
            }

            TaskClient.PcapDownloadTaskResult taskResult = response.getData();
            return "task_" + taskResult.taskId();

        } catch (Exception e) {
            log.error("创建异步下载任务失败: downloadId={}", downloadId, e);
            throw new RuntimeException("创建异步任务失败: " + e.getMessage(), e);
        }
    }
}
