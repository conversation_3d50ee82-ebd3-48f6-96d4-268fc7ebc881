package com.geeksec.nta.alarm.interfaces.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 攻击链响应
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttackChainResponse {
    
    /**
     * 攻击链ID
     */
    private String chainId;
    
    /**
     * 攻击链名称
     */
    private String chainName;
    
    /**
     * 攻击链描述
     */
    private String description;
    
    /**
     * 攻击链阶段
     */
    private List<AttackStage> stages;
    
    /**
     * 涉及的告警数量
     */
    private Long alarmCount;
    
    /**
     * 攻击者信息
     */
    private AttackerInfo attacker;
    
    /**
     * 受害者信息
     */
    private List<VictimInfo> victims;
    
    /**
     * 攻击时间范围
     */
    private TimeRange timeRange;
    
    /**
     * 威胁等级
     */
    private Integer threatLevel;
    
    /**
     * 攻击链状态
     */
    private ChainStatus status;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 攻击阶段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackStage {
        /**
         * 阶段ID
         */
        private String stageId;
        
        /**
         * 阶段名称
         */
        private String stageName;
        
        /**
         * 阶段描述
         */
        private String description;
        
        /**
         * 阶段顺序
         */
        private Integer order;
        
        /**
         * 相关告警
         */
        private List<StageAlarm> alarms;
        
        /**
         * 阶段时间
         */
        private LocalDateTime stageTime;
        
        /**
         * 攻击技术
         */
        private List<String> techniques;
    }
    
    /**
     * 阶段告警
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StageAlarm {
        private String alarmId;
        private String alarmType;
        private String alarmName;
        private LocalDateTime alarmTime;
        private Integer threatLevel;
    }
    
    /**
     * 攻击者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackerInfo {
        private String ip;
        private String hostname;
        private String location;
        private String organization;
        private Map<String, Object> attributes;
    }
    
    /**
     * 受害者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VictimInfo {
        private String ip;
        private String hostname;
        private String department;
        private List<String> affectedServices;
        private Map<String, Object> attributes;
    }
    
    /**
     * 时间范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange {
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Long duration; // 持续时间（秒）
    }
    
    /**
     * 攻击链状态枚举
     */
    public enum ChainStatus {
        ACTIVE("active", "活跃"),
        COMPLETED("completed", "已完成"),
        INTERRUPTED("interrupted", "已中断"),
        INVESTIGATING("investigating", "调查中"),
        RESOLVED("resolved", "已解决");
        
        private final String code;
        private final String description;
        
        ChainStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
