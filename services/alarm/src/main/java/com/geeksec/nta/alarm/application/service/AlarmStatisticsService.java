package com.geeksec.nta.alarm.application.service;

import com.geeksec.nta.alarm.application.query.AlarmStatisticsQuery;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmStatisticsResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmTrendResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AttackChainResponse;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警统计服务接口
 * 负责告警数据的统计分析
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmStatisticsService {
    
    /**
     * 获取告警统计数据
     * 
     * @param query 统计查询条件
     * @return 统计结果
     */
    AlarmStatisticsResponse getAlarmStatistics(AlarmStatisticsQuery query);
    
    /**
     * 获取告警趋势数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param granularity 时间粒度
     * @return 趋势数据
     */
    AlarmTrendResponse getAlarmTrend(LocalDateTime startTime, LocalDateTime endTime, String granularity);
    
    /**
     * 获取攻击链统计
     * 
     * @param query 查询条件
     * @return 攻击链统计
     */
    List<AttackChainResponse> getAttackChainStatistics(AlarmStatisticsQuery query);
    
    /**
     * 获取实时告警统计
     * 
     * @return 实时统计数据
     */
    RealtimeStatistics getRealtimeStatistics();
    
    /**
     * 获取告警热力图数据
     * 
     * @param query 查询条件
     * @return 热力图数据
     */
    HeatmapData getAlarmHeatmap(AlarmStatisticsQuery query);
    
    /**
     * 获取告警分布统计
     * 
     * @param dimension 统计维度
     * @param query 查询条件
     * @return 分布统计
     */
    DistributionStatistics getAlarmDistribution(String dimension, AlarmStatisticsQuery query);
    
    /**
     * 获取告警排行榜
     * 
     * @param rankType 排行类型
     * @param query 查询条件
     * @param limit 限制数量
     * @return 排行榜数据
     */
    RankingData getAlarmRanking(String rankType, AlarmStatisticsQuery query, int limit);
    
    /**
     * 获取告警对比分析
     * 
     * @param currentQuery 当前周期查询
     * @param previousQuery 对比周期查询
     * @return 对比分析结果
     */
    ComparisonAnalysis getAlarmComparison(AlarmStatisticsQuery currentQuery, AlarmStatisticsQuery previousQuery);
    
    /**
     * 实时统计数据
     */
    record RealtimeStatistics(
        long totalAlarms,
        long todayAlarms,
        long pendingAlarms,
        long highRiskAlarms,
        double avgResponseTime,
        List<RecentAlarm> recentAlarms
    ) {}
    
    /**
     * 最近告警
     */
    record RecentAlarm(
        String alarmId,
        String alarmType,
        String alarmName,
        LocalDateTime alarmTime,
        int threatLevel
    ) {}
    
    /**
     * 热力图数据
     */
    record HeatmapData(
        List<HeatmapPoint> points,
        HeatmapMetrics metrics
    ) {}
    
    /**
     * 热力图点
     */
    record HeatmapPoint(
        String x,
        String y,
        long value,
        double intensity
    ) {}
    
    /**
     * 热力图指标
     */
    record HeatmapMetrics(
        long maxValue,
        long minValue,
        double avgValue,
        int totalPoints
    ) {}
    
    /**
     * 分布统计
     */
    record DistributionStatistics(
        String dimension,
        List<DistributionItem> items,
        DistributionSummary summary
    ) {}
    
    /**
     * 分布项
     */
    record DistributionItem(
        String label,
        long count,
        double percentage
    ) {}
    
    /**
     * 分布摘要
     */
    record DistributionSummary(
        long totalCount,
        int categoryCount,
        String topCategory,
        double concentration
    ) {}
    
    /**
     * 排行榜数据
     */
    record RankingData(
        String rankType,
        List<RankingItem> items,
        RankingMetrics metrics
    ) {}
    
    /**
     * 排行项
     */
    record RankingItem(
        int rank,
        String name,
        long value,
        Map<String, Object> details
    ) {}
    
    /**
     * 排行指标
     */
    record RankingMetrics(
        long totalValue,
        double avgValue,
        long maxValue,
        long minValue
    ) {}
    
    /**
     * 对比分析
     */
    record ComparisonAnalysis(
        ComparisonPeriod currentPeriod,
        ComparisonPeriod previousPeriod,
        ComparisonMetrics comparison
    ) {}
    
    /**
     * 对比周期
     */
    record ComparisonPeriod(
        LocalDateTime startTime,
        LocalDateTime endTime,
        long totalAlarms,
        Map<String, Long> breakdown
    ) {}
    
    /**
     * 对比指标
     */
    record ComparisonMetrics(
        double changeRate,
        long absoluteChange,
        String trend,
        List<String> insights
    ) {}

    /**
     * 攻击者统计
     */
    record AttackerStatistics(
        String attackerIp,
        String location,
        long alarmCount,
        List<String> alarmTypes,
        double riskScore
    ) {}

    /**
     * 受害者统计
     */
    record VictimStatistics(
        String victimIp,
        String hostname,
        long alarmCount,
        List<String> alarmTypes,
        double riskScore
    ) {}

    /**
     * 告警类型统计
     */
    record AlarmTypeStatistics(
        String alarmType,
        String alarmTypeName,
        long count,
        double percentage,
        String trend
    ) {}

    /**
     * 严重程度统计
     */
    record SeverityStatistics(
        int severity,
        String severityName,
        long count,
        double percentage,
        String color
    ) {}

    /**
     * 获取TOP攻击者统计
     *
     * @param query 查询条件
     * @param limit 限制数量
     * @return 攻击者统计列表
     */
    List<AttackerStatistics> getTopAttackers(AlarmStatisticsQuery query, int limit);

    /**
     * 获取TOP受害者统计
     *
     * @param query 查询条件
     * @param limit 限制数量
     * @return 受害者统计列表
     */
    List<VictimStatistics> getTopVictims(AlarmStatisticsQuery query, int limit);

    /**
     * 获取告警类型统计
     *
     * @param query 查询条件
     * @return 告警类型统计列表
     */
    List<AlarmTypeStatistics> getAlarmTypeStatistics(AlarmStatisticsQuery query);

    /**
     * 获取严重程度统计
     *
     * @param query 查询条件
     * @return 严重程度统计列表
     */
    List<SeverityStatistics> getSeverityStatistics(AlarmStatisticsQuery query);
}
