package com.geeksec.nta.alarm.infrastructure.client;

import com.geeksec.common.dto.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Task服务Feign客户端
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@FeignClient(name = "task-service", path = "/api/v1")
public interface TaskClient {

    /**
     * 创建PCAP下载任务
     * 
     * @param request 下载请求
     * @return 任务结果
     */
    @PostMapping("/pcap-download/create")
    ApiResponse<PcapDownloadTaskResult> createPcapDownloadTask(@RequestBody PcapDownloadRequest request);

    /**
     * 查询下载任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/pcap-download/{taskId}/status")
    ApiResponse<PcapDownloadTaskResult> getDownloadTaskStatus(@PathVariable("taskId") Integer taskId);

    /**
     * 取消下载任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    @PostMapping("/pcap-download/{taskId}/cancel")
    ApiResponse<Boolean> cancelDownloadTask(@PathVariable("taskId") Integer taskId, 
                                           @RequestParam("userId") String userId);

    /**
     * 删除下载任务
     * 
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    @DeleteMapping("/pcap-download/{taskId}")
    ApiResponse<Boolean> deleteDownloadTask(@PathVariable("taskId") Integer taskId, 
                                           @RequestParam("userId") String userId);

    /**
     * 获取用户的下载任务列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页大小
     * @return 任务列表
     */
    @GetMapping("/pcap-download/user/{userId}")
    ApiResponse<List<PcapDownloadTaskResult>> getUserDownloadTasks(
            @PathVariable("userId") String userId,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size);

    /**
     * PCAP下载请求
     */
    record PcapDownloadRequest(
        String userId,
        String alarmType,
        Long alarmTime,
        List<String> sessionIds,
        String description
    ) {}

    /**
     * PCAP下载任务结果
     */
    record PcapDownloadTaskResult(
        Integer taskId,
        String userId,
        String alarmType,
        Long alarmTime,
        List<String> sessionIds,
        String status,
        Integer progress,
        Integer totalFileCount,
        Integer processedFileCount,
        Long archiveFileSize,
        String downloadUrl,
        String errorMessage,
        LocalDateTime createTime,
        LocalDateTime startTime,
        LocalDateTime completeTime,
        LocalDateTime expireTime
    ) {}
}
