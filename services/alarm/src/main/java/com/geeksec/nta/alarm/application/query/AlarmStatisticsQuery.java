package com.geeksec.nta.alarm.application.query;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警统计查询条件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmStatisticsQuery {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 告警类型列表
     */
    private List<String> alarmTypes;
    
    /**
     * 威胁等级列表
     */
    private List<Integer> threatLevels;
    
    /**
     * 统计维度
     */
    private StatisticsDimension dimension;
    
    /**
     * 时间粒度
     */
    private TimeGranularity timeGranularity;
    
    /**
     * 分组字段
     */
    private List<String> groupByFields;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向
     */
    private SortDirection sortDirection;
    
    /**
     * 限制数量
     */
    private Integer limit;
    
    /**
     * 是否包含趋势数据
     */
    private Boolean includeTrend;
    
    /**
     * 统计维度枚举
     */
    public enum StatisticsDimension {
        ALARM_TYPE("alarm_type", "按告警类型统计"),
        THREAT_LEVEL("threat_level", "按威胁等级统计"),
        STATUS("status", "按处理状态统计"),
        ATTACKER_IP("attacker_ip", "按攻击者IP统计"),
        VICTIM_IP("victim_ip", "按受害者IP统计"),
        TIME("time", "按时间统计"),
        ATTACK_CHAIN("attack_chain", "按攻击链统计");
        
        private final String code;
        private final String description;
        
        StatisticsDimension(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 时间粒度枚举
     */
    public enum TimeGranularity {
        HOUR("hour", "小时"),
        DAY("day", "天"),
        WEEK("week", "周"),
        MONTH("month", "月");
        
        private final String code;
        private final String description;
        
        TimeGranularity(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 排序方向枚举
     */
    public enum SortDirection {
        ASC("asc", "升序"),
        DESC("desc", "降序");
        
        private final String code;
        private final String description;
        
        SortDirection(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
