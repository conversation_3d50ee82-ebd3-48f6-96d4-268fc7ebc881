package com.geeksec.nta.alarm.interfaces.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警研判响应
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmJudgeResponse {
    
    /**
     * 研判ID
     */
    private String judgeId;
    
    /**
     * 告警ID
     */
    private String alarmId;
    
    /**
     * 研判结果
     */
    private JudgeResult result;
    
    /**
     * 威胁等级评估
     */
    private ThreatAssessment threatAssessment;
    
    /**
     * 关联分析结果
     */
    private CorrelationAnalysis correlationAnalysis;
    
    /**
     * 攻击链分析
     */
    private AttackChainAnalysis attackChainAnalysis;
    
    /**
     * 研判建议
     */
    private List<JudgmentSuggestion> suggestions;
    
    /**
     * 研判时间
     */
    private LocalDateTime judgeTime;
    
    /**
     * 研判耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 研判结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JudgeResult {
        private String conclusion;
        private Double confidence;
        private String reasoning;
        private Map<String, Object> evidence;
    }
    
    /**
     * 威胁评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatAssessment {
        private Integer currentLevel;
        private Integer suggestedLevel;
        private String levelReason;
        private List<ThreatFactor> factors;
    }
    
    /**
     * 威胁因子
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatFactor {
        private String name;
        private Double weight;
        private Double score;
        private String description;
    }
    
    /**
     * 关联分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationAnalysis {
        private List<RelatedAlarm> relatedAlarms;
        private List<AlarmCluster> clusters;
        private Integer totalRelated;
    }
    
    /**
     * 相关告警
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelatedAlarm {
        private String alarmId;
        private String alarmType;
        private Double similarity;
        private String relation;
    }
    
    /**
     * 告警集群
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmCluster {
        private String clusterId;
        private List<String> alarmIds;
        private String clusterType;
        private Double similarity;
    }
    
    /**
     * 攻击链分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackChainAnalysis {
        private String chainId;
        private List<AttackStage> stages;
        private Double completeness;
        private String description;
    }
    
    /**
     * 攻击阶段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackStage {
        private String stageId;
        private String stageName;
        private Integer order;
        private List<String> techniques;
        private LocalDateTime stageTime;
    }
    
    /**
     * 研判建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JudgmentSuggestion {
        private String suggestionId;
        private String title;
        private String description;
        private String type;
        private Integer priority;
        private List<String> actions;
    }
}
