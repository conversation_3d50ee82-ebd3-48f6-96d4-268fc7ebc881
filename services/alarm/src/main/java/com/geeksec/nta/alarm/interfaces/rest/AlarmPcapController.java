package com.geeksec.nta.alarm.interfaces.rest;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.application.service.integration.AlarmExportService;
import com.geeksec.nta.alarm.application.service.integration.AlarmExportService.PcapDownloadResponse;
import com.geeksec.nta.alarm.application.service.integration.AlarmExportService.PcapDownloadTaskStatus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 告警PCAP下载控制器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/alarm/pcap")
@RequiredArgsConstructor
@Validated
@Tag(name = "告警PCAP下载", description = "告警关联会话PCAP文件下载")
public class AlarmPcapController {

    private final AlarmExportService alarmExportService;

    /**
     * 创建告警会话PCAP下载任务
     */
    @PostMapping("/download")
    @Operation(summary = "创建PCAP下载任务", description = "为告警关联的会话创建PCAP文件下载任务")
    public ApiResponse<PcapDownloadResponse> createPcapDownloadTask(
            @Parameter(description = "PCAP下载请求", required = true)
            @Valid @RequestBody PcapDownloadRequest request) {
        try {
            log.info("创建告警PCAP下载任务: userId={}, sessionCount={}", 
                    request.userId(), request.sessionIds().size());

            PcapDownloadResponse response = alarmExportService.prepareAlarmSessionPcap(
                request.userId(),
                request.sessionIds(),
                request.alarmType(),
                request.alarmTime()
            );

            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("创建PCAP下载任务失败", e);
            return ApiResponse.error("创建下载任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询PCAP下载任务状态
     */
    @GetMapping("/download/{taskId}/status")
    @Operation(summary = "查询下载任务状态", description = "查询PCAP下载任务的执行状态和进度")
    public ApiResponse<PcapDownloadTaskStatus> getDownloadTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId) {
        try {
            PcapDownloadTaskStatus status = alarmExportService.getPcapDownloadTaskStatus(taskId);
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("查询下载任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消PCAP下载任务
     */
    @PostMapping("/download/{taskId}/cancel")
    @Operation(summary = "取消下载任务", description = "取消正在执行的PCAP下载任务")
    public ApiResponse<Boolean> cancelDownloadTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable Integer taskId,
            @Parameter(description = "用户ID", required = true)
            @RequestParam String userId) {
        try {
            boolean success = alarmExportService.cancelPcapDownloadTask(taskId, userId);
            return ApiResponse.success(success);
        } catch (Exception e) {
            log.error("取消下载任务失败: taskId={}, userId={}", taskId, userId, e);
            return ApiResponse.error("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的PCAP下载任务列表
     */
    @GetMapping("/download/user/{userId}")
    @Operation(summary = "查询用户下载任务", description = "查询用户的PCAP下载任务列表")
    public ApiResponse<List<PcapDownloadTaskStatus>> getUserDownloadTasks(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小", example = "10")
            @RequestParam(defaultValue = "10") int size) {
        try {
            List<PcapDownloadTaskStatus> tasks = alarmExportService.getUserPcapDownloadTasks(userId, page, size);
            return ApiResponse.success(tasks);
        } catch (Exception e) {
            log.error("查询用户下载任务失败: userId={}", userId, e);
            return ApiResponse.error("查询用户任务失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建PCAP下载任务
     */
    @PostMapping("/download/batch")
    @Operation(summary = "批量创建下载任务", description = "为多个告警批量创建PCAP下载任务")
    public ApiResponse<List<PcapDownloadResponse>> createBatchPcapDownloadTasks(
            @Parameter(description = "批量下载请求", required = true)
            @Valid @RequestBody BatchPcapDownloadRequest request) {
        try {
            log.info("批量创建告警PCAP下载任务: userId={}, alarmCount={}", 
                    request.userId(), request.alarmRequests().size());

            List<PcapDownloadResponse> responses = request.alarmRequests().stream()
                    .map(alarmRequest -> alarmExportService.prepareAlarmSessionPcap(
                        request.userId(),
                        alarmRequest.sessionIds(),
                        alarmRequest.alarmType(),
                        alarmRequest.alarmTime()
                    ))
                    .toList();

            return ApiResponse.success(responses);
        } catch (Exception e) {
            log.error("批量创建PCAP下载任务失败", e);
            return ApiResponse.error("批量创建下载任务失败: " + e.getMessage());
        }
    }

    /**
     * PCAP下载请求
     */
    public record PcapDownloadRequest(
        @NotNull(message = "用户ID不能为空")
        String userId,
        
        @NotNull(message = "告警类型不能为空")
        String alarmType,
        
        @NotNull(message = "告警时间不能为空")
        Long alarmTime,
        
        @NotEmpty(message = "会话ID列表不能为空")
        List<String> sessionIds
    ) {}

    /**
     * 批量PCAP下载请求
     */
    public record BatchPcapDownloadRequest(
        @NotNull(message = "用户ID不能为空")
        String userId,
        
        @NotEmpty(message = "告警请求列表不能为空")
        List<AlarmPcapRequest> alarmRequests
    ) {}

    /**
     * 告警PCAP请求
     */
    public record AlarmPcapRequest(
        @NotNull(message = "告警类型不能为空")
        String alarmType,
        
        @NotNull(message = "告警时间不能为空")
        Long alarmTime,
        
        @NotEmpty(message = "会话ID列表不能为空")
        List<String> sessionIds
    ) {}
}
