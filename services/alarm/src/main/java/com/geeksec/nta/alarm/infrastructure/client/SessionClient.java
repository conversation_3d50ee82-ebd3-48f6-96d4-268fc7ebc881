package com.geeksec.nta.alarm.infrastructure.client;

import com.geeksec.common.dto.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Session服务Feign客户端
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@FeignClient(name = "session-service", path = "/api/v1/session")
public interface SessionClient {

    /**
     * 根据会话ID列表查询PCAP文件信息
     * 
     * @param sessionIds 会话ID列表
     * @return PCAP文件信息列表
     */
    @PostMapping("/pcap/files")
    ApiResponse<List<SessionPcapInfo>> getSessionPcapFiles(@RequestBody List<String> sessionIds);

    /**
     * 验证会话ID列表的有效性
     * 
     * @param sessionIds 会话ID列表
     * @return 验证结果
     */
    @PostMapping("/pcap/validate")
    ApiResponse<SessionValidationResult> validateSessionIds(@RequestBody List<String> sessionIds);

    /**
     * 批量查询会话基本信息
     *
     * @param sessionIds 会话ID列表
     * @return 会话基本信息列表
     */
    @PostMapping("/pcap/batch")
    ApiResponse<List<SessionBasicInfo>> getSessionBasicInfo(@RequestBody List<String> sessionIds);

    /**
     * 统一的PCAP下载接口
     *
     * @param request PCAP下载请求
     * @return 下载响应
     */
    @PostMapping("/pcap/download")
    ApiResponse<PcapDownloadResponse> downloadSessionsPcap(@RequestBody PcapDownloadRequest request);

    /**
     * 会话PCAP文件信息
     */
    record SessionPcapInfo(
        String sessionId,
        String pcapFilePath,
        Long fileSize,
        LocalDateTime sessionStartTime,
        LocalDateTime sessionEndTime,
        String srcIp,
        String dstIp,
        Integer srcPort,
        Integer dstPort,
        Integer protocol
    ) {}

    /**
     * 会话验证结果
     */
    record SessionValidationResult(
        List<String> validSessionIds,
        List<String> invalidSessionIds,
        int totalCount,
        int validCount,
        int invalidCount
    ) {}

    /**
     * 会话基本信息
     */
    record SessionBasicInfo(
        String sessionId,
        String srcIp,
        String dstIp,
        Integer srcPort,
        Integer dstPort,
        Integer protocol,
        LocalDateTime sessionStartTime,
        LocalDateTime sessionEndTime
    ) {}

    /**
     * 批量下载请求
     */
    record BatchDownloadRequest(
        List<String> sessionIds,
        String downloadName,
        String format
    ) {}

    /**
     * 批量下载响应
     */
    record BatchDownloadResponse(
        String downloadId,
        String downloadUrl,
        int totalFiles,
        long totalSize,
        String status,
        LocalDateTime expireTime
    ) {}

    /**
     * PCAP下载请求
     */
    record PcapDownloadRequest(
        List<String> sessionIds,
        String downloadName,
        String requestSource,
        String userId,
        java.util.Map<String, Object> metadata
    ) {}

    /**
     * PCAP下载响应
     */
    record PcapDownloadResponse(
        String downloadId,
        String status,
        String downloadUrl,
        String taskId,
        int totalSessions,
        long estimatedSize,
        String message,
        LocalDateTime createTime,
        LocalDateTime expireTime
    ) {}
}
