package com.geeksec.nta.alarm.application.query;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警导出查询条件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmExportQuery {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 告警类型列表
     */
    private List<String> alarmTypes;
    
    /**
     * 告警状态列表
     */
    private List<Integer> statusList;
    
    /**
     * 威胁等级列表
     */
    private List<Integer> threatLevels;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 攻击者IP列表
     */
    private List<String> attackerIps;
    
    /**
     * 受害者IP列表
     */
    private List<String> victimIps;
    
    /**
     * 关键词搜索
     */
    private String keyword;
    
    /**
     * 导出格式
     */
    private ExportFormat format;
    
    /**
     * 导出字段列表
     */
    private List<String> exportFields;
    
    /**
     * 最大导出数量
     */
    private Integer maxCount;
    
    /**
     * 是否包含原始数据
     */
    private Boolean includeRawData;
    
    /**
     * 导出格式枚举
     */
    public enum ExportFormat {
        CSV("csv", "CSV格式"),
        EXCEL("excel", "Excel格式"),
        JSON("json", "JSON格式"),
        PDF("pdf", "PDF格式");
        
        private final String code;
        private final String description;
        
        ExportFormat(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
