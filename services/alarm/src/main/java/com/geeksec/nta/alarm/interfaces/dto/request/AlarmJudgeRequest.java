package com.geeksec.nta.alarm.interfaces.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 告警研判请求
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmJudgeRequest {
    
    /**
     * 告警ID
     */
    private String alarmId;
    
    /**
     * 研判类型
     */
    private JudgeType judgeType;
    
    /**
     * 研判深度
     */
    private Integer depth;
    
    /**
     * 是否包含关联分析
     */
    private Boolean includeCorrelation;
    
    /**
     * 是否生成攻击链
     */
    private Boolean generateAttackChain;
    
    /**
     * 是否评估威胁等级
     */
    private Boolean assessThreatLevel;
    
    /**
     * 扩展参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 研判类型枚举
     */
    public enum JudgeType {
        QUICK("quick", "快速研判"),
        DETAILED("detailed", "详细研判"),
        COMPREHENSIVE("comprehensive", "全面研判");
        
        private final String code;
        private final String description;
        
        JudgeType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
