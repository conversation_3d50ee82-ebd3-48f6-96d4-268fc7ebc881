package com.geeksec.nta.alarm.application.service;

import com.geeksec.nta.alarm.domain.entity.AlarmJudgeEdgeEntity;
import com.geeksec.nta.alarm.domain.entity.AlarmJudgeVertexEntity;
import com.geeksec.nta.alarm.interfaces.dto.request.AlarmJudgeRequest;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmJudgeResponse;

import java.util.List;

/**
 * 告警研判服务接口
 * 负责告警的智能分析和研判
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmJudgeService {
    
    /**
     * 执行告警研判
     * 
     * @param request 研判请求
     * @return 研判结果
     */
    AlarmJudgeResponse judgeAlarm(AlarmJudgeRequest request);
    
    /**
     * 获取告警研判图谱数据
     * 
     * @param alarmId 告警ID
     * @return 图谱数据
     */
    AlarmGraphData getAlarmGraphData(String alarmId);
    
    /**
     * 分析告警关联关系
     * 
     * @param alarmIds 告警ID列表
     * @return 关联分析结果
     */
    AlarmCorrelationResult analyzeAlarmCorrelation(List<String> alarmIds);
    
    /**
     * 生成攻击链分析
     * 
     * @param alarmId 告警ID
     * @return 攻击链分析结果
     */
    AttackChainAnalysis generateAttackChain(String alarmId);
    
    /**
     * 评估告警威胁等级
     * 
     * @param alarmId 告警ID
     * @return 威胁等级评估结果
     */
    ThreatLevelAssessment assessThreatLevel(String alarmId);
    
    /**
     * 获取研判建议
     * 
     * @param alarmId 告警ID
     * @return 研判建议
     */
    List<JudgmentSuggestion> getJudgmentSuggestions(String alarmId);
    
    /**
     * 告警图谱数据
     */
    record AlarmGraphData(
        List<AlarmJudgeVertexEntity> vertices,
        List<AlarmJudgeEdgeEntity> edges,
        GraphMetadata metadata
    ) {}
    
    /**
     * 图谱元数据
     */
    record GraphMetadata(
        int vertexCount,
        int edgeCount,
        String centerAlarmId,
        int maxDepth
    ) {}
    
    /**
     * 告警关联分析结果
     */
    record AlarmCorrelationResult(
        List<AlarmCluster> clusters,
        List<AlarmRelation> relations,
        CorrelationStatistics statistics
    ) {}
    
    /**
     * 告警集群
     */
    record AlarmCluster(
        String clusterId,
        List<String> alarmIds,
        String clusterType,
        double similarity
    ) {}
    
    /**
     * 告警关联关系
     */
    record AlarmRelation(
        String sourceAlarmId,
        String targetAlarmId,
        String relationType,
        double confidence
    ) {}
    
    /**
     * 关联统计
     */
    record CorrelationStatistics(
        int totalAlarms,
        int clusterCount,
        int relationCount,
        double avgSimilarity
    ) {}
    
    /**
     * 攻击链分析
     */
    record AttackChainAnalysis(
        String chainId,
        List<AttackStage> stages,
        AttackChainMetrics metrics
    ) {}
    
    /**
     * 攻击阶段
     */
    record AttackStage(
        String stageId,
        String stageName,
        List<String> alarmIds,
        int order
    ) {}
    
    /**
     * 攻击链指标
     */
    record AttackChainMetrics(
        int stageCount,
        int totalAlarms,
        long duration,
        double completeness
    ) {}
    
    /**
     * 威胁等级评估
     */
    record ThreatLevelAssessment(
        int currentLevel,
        int suggestedLevel,
        List<ThreatFactor> factors,
        String reasoning
    ) {}
    
    /**
     * 威胁因子
     */
    record ThreatFactor(
        String factorName,
        double weight,
        double score,
        String description
    ) {}
    
    /**
     * 研判建议
     */
    record JudgmentSuggestion(
        String suggestionId,
        String title,
        String description,
        SuggestionType type,
        int priority
    ) {}
    
    /**
     * 建议类型
     */
    enum SuggestionType {
        INVESTIGATION,
        MITIGATION,
        ESCALATION,
        MONITORING
    }
}
