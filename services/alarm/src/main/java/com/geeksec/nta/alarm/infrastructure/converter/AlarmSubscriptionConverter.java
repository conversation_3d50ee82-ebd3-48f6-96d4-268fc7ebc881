package com.geeksec.nta.alarm.infrastructure.converter;

import com.geeksec.nta.alarm.domain.aggregate.subscription.AlarmSubscription;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSubscriptionResponse;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警订阅转换器
 * 负责实体与DTO之间的转换
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Component
public class AlarmSubscriptionConverter {
    
    /**
     * 转换为响应对象
     * 
     * @param subscription 订阅实体
     * @return 响应对象
     */
    public AlarmSubscriptionResponse toResponse(AlarmSubscription subscription) {
        if (subscription == null) {
            return null;
        }
        
        return AlarmSubscriptionResponse.builder()
                .subscriptionId(subscription.getId())
                .userId(subscription.getUserId())
                .subscriptionName(subscription.getSubscriptionName())
                .description(subscription.getDescription())
                .enabled(subscription.getEnabled())
                .priorityLevel(subscription.getPriorityLevel())
                .matchRules(subscription.getMatchRules())
                .notificationChannels(subscription.getNotificationChannels())
                .frequencyType(subscription.getFrequencyType())
                .frequencyConfig(subscription.getFrequencyConfig())
                .quietHoursEnabled(subscription.getQuietHoursEnabled())
                .quietHoursConfig(subscription.getQuietHoursConfig())
                .triggerCount(subscription.getTriggerCount())
                .lastTriggeredTime(subscription.getLastTriggeredTime())
                .createdBy(subscription.getCreatedBy())
                .createdTime(subscription.getCreatedTime())
                .updatedBy(subscription.getUpdatedBy())
                .updatedTime(subscription.getUpdatedTime())
                .build();
    }
    
    /**
     * 批量转换为响应对象
     * 
     * @param subscriptions 订阅实体列表
     * @return 响应对象列表
     */
    public List<AlarmSubscriptionResponse> toResponses(List<AlarmSubscription> subscriptions) {
        if (subscriptions == null) {
            return List.of();
        }
        
        return subscriptions.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
