package com.geeksec.nta.alarm.interfaces.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警统计请求
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmStatisticsRequest {
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 告警类型列表
     */
    private List<String> alarmTypes;
    
    /**
     * 威胁等级列表
     */
    private List<Integer> threatLevels;
    
    /**
     * 状态列表
     */
    private List<Integer> statusList;
    
    /**
     * 统计维度
     */
    private String dimension;
    
    /**
     * 时间粒度
     */
    private String timeGranularity;
    
    /**
     * 分组字段
     */
    private List<String> groupBy;
    
    /**
     * 排序字段
     */
    private String orderBy;
    
    /**
     * 排序方向
     */
    private String sortDirection;
    
    /**
     * 限制数量
     */
    private Integer limit;
    
    /**
     * 是否包含趋势
     */
    private Boolean includeTrend;
    
    /**
     * 用户ID
     */
    private String userId;
}
