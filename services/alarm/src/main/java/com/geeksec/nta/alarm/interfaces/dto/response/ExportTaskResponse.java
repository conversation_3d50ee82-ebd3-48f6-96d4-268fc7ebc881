package com.geeksec.nta.alarm.interfaces.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 导出任务响应
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportTaskResponse {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 进度百分比 (0-100)
     */
    private Integer progress;
    
    /**
     * 导出格式
     */
    private String format;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 下载URL
     */
    private String downloadUrl;
    
    /**
     * 导出记录数
     */
    private Integer recordCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("pending", "等待中"),
        RUNNING("running", "执行中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "失败"),
        CANCELLED("cancelled", "已取消"),
        EXPIRED("expired", "已过期");
        
        private final String code;
        private final String description;
        
        TaskStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
