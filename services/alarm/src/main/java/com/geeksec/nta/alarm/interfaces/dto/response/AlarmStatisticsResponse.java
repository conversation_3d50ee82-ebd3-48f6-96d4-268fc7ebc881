package com.geeksec.nta.alarm.interfaces.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警统计响应
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmStatisticsResponse {
    
    /**
     * 统计时间范围
     */
    private TimeRange timeRange;
    
    /**
     * 总告警数
     */
    private Long totalAlarms;
    
    /**
     * 未处理告警数
     */
    private Long pendingAlarms;
    
    /**
     * 处理中告警数
     */
    private Long processingAlarms;
    
    /**
     * 已处理告警数
     */
    private Long resolvedAlarms;
    
    /**
     * 已关闭告警数
     */
    private Long closedAlarms;
    
    /**
     * 按告警类型统计
     */
    private List<AlarmTypeStatistics> alarmTypeStats;
    
    /**
     * 按威胁等级统计
     */
    private List<ThreatLevelStatistics> threatLevelStats;
    
    /**
     * 按状态统计
     */
    private List<StatusStatistics> statusStats;
    
    /**
     * 热门攻击者IP
     */
    private List<TopAttackerStatistics> topAttackers;
    
    /**
     * 热门受害者IP
     */
    private List<TopVictimStatistics> topVictims;
    
    /**
     * 扩展统计数据
     */
    private Map<String, Object> extendedStats;
    
    /**
     * 时间范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange {
        private LocalDateTime startTime;
        private LocalDateTime endTime;
    }
    
    /**
     * 告警类型统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmTypeStatistics {
        private String alarmType;
        private String alarmTypeName;
        private Long count;
        private Double percentage;
    }
    
    /**
     * 威胁等级统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreatLevelStatistics {
        private Integer threatLevel;
        private String threatLevelName;
        private Long count;
        private Double percentage;
    }
    
    /**
     * 状态统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusStatistics {
        private Integer status;
        private String statusName;
        private Long count;
        private Double percentage;
    }
    
    /**
     * 热门攻击者统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopAttackerStatistics {
        private String attackerIp;
        private String location;
        private Long alarmCount;
        private List<String> alarmTypes;
    }
    
    /**
     * 热门受害者统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopVictimStatistics {
        private String victimIp;
        private String hostname;
        private Long alarmCount;
        private List<String> alarmTypes;
    }
}
