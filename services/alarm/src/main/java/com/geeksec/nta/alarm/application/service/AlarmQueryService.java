package com.geeksec.nta.alarm.application.service;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.AlarmListQuery;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmDetailResponse;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmListResponse;

import java.util.List;
import java.util.Optional;

/**
 * 告警查询服务接口
 * 负责告警数据的查询操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmQueryService {
    
    /**
     * 分页查询告警列表
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVo<AlarmListResponse> queryAlarms(AlarmListQuery query);
    
    /**
     * 根据ID查询告警详情
     * 
     * @param alarmId 告警ID
     * @return 告警详情
     */
    Optional<AlarmDetailResponse> getAlarmDetail(String alarmId);
    
    /**
     * 查询所有告警
     * 
     * @return 告警列表
     */
    List<AlarmListResponse> getAllAlarms();
    
    /**
     * 根据条件查询告警数量
     * 
     * @param query 查询条件
     * @return 告警数量
     */
    long countAlarms(AlarmListQuery query);
    
    /**
     * 查询最近的告警
     * 
     * @param limit 限制数量
     * @return 最近告警列表
     */
    List<AlarmListResponse> getRecentAlarms(int limit);
    
    /**
     * 根据状态查询告警
     * 
     * @param status 告警状态
     * @return 告警列表
     */
    List<AlarmListResponse> getAlarmsByStatus(String status);
    
    /**
     * 根据威胁等级查询告警
     * 
     * @param threatLevel 威胁等级
     * @return 告警列表
     */
    List<AlarmListResponse> getAlarmsByThreatLevel(int threatLevel);
    
    /**
     * 根据告警类型查询告警
     * 
     * @param alarmType 告警类型
     * @return 告警列表
     */
    List<AlarmListResponse> getAlarmsByType(String alarmType);
}
