package com.geeksec.nta.alarm.interfaces.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警趋势响应
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmTrendResponse {
    
    /**
     * 时间粒度
     */
    private String timeGranularity;
    
    /**
     * 统计时间范围
     */
    private TimeRange timeRange;
    
    /**
     * 趋势数据点
     */
    private List<TrendDataPoint> trendData;
    
    /**
     * 总体趋势
     */
    private TrendSummary summary;
    
    /**
     * 时间范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange {
        private LocalDateTime startTime;
        private LocalDateTime endTime;
    }
    
    /**
     * 趋势数据点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendDataPoint {
        /**
         * 时间点
         */
        private LocalDateTime timestamp;
        
        /**
         * 时间标签（用于显示）
         */
        private String timeLabel;
        
        /**
         * 告警总数
         */
        private Long totalCount;
        
        /**
         * 新增告警数
         */
        private Long newCount;
        
        /**
         * 处理告警数
         */
        private Long resolvedCount;
        
        /**
         * 高危告警数
         */
        private Long highRiskCount;
        
        /**
         * 中危告警数
         */
        private Long mediumRiskCount;
        
        /**
         * 低危告警数
         */
        private Long lowRiskCount;
        
        /**
         * 按告警类型分组的数据
         */
        private List<AlarmTypeData> alarmTypeData;
    }
    
    /**
     * 告警类型数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlarmTypeData {
        private String alarmType;
        private String alarmTypeName;
        private Long count;
    }
    
    /**
     * 趋势摘要
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendSummary {
        /**
         * 总告警数
         */
        private Long totalAlarms;
        
        /**
         * 平均每日告警数
         */
        private Double avgDailyAlarms;
        
        /**
         * 峰值告警数
         */
        private Long peakAlarms;
        
        /**
         * 峰值时间
         */
        private LocalDateTime peakTime;
        
        /**
         * 增长率（相比上一周期）
         */
        private Double growthRate;
        
        /**
         * 趋势方向
         */
        private TrendDirection trendDirection;
    }
    
    /**
     * 趋势方向枚举
     */
    public enum TrendDirection {
        INCREASING("increasing", "上升"),
        DECREASING("decreasing", "下降"),
        STABLE("stable", "稳定"),
        FLUCTUATING("fluctuating", "波动");
        
        private final String code;
        private final String description;
        
        TrendDirection(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
