package com.geeksec.nta.alarm.domain.aggregate.notification;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知结果聚合根 - 对应notification_results表
 * 记录告警通知的发送结果和状态，专注于核心业务需求
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "notification_results", comment = "通知结果记录表")
public class NotificationResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 通知日志ID
     */
    @Id(keyType = KeyType.Generator)
    @Column("id")
    private String id;
    
    /**
     * 告警ID
     */
    @Column("alarm_id")
    private String alarmId;
    
    /**
     * 订阅ID
     */
    @Column("subscription_id")
    private String subscriptionId;
    
    /**
     * 通知渠道 (EMAIL, SMS, WEBHOOK, KAFKA等)
     */
    @Column("channel")
    private String channel;
    
    /**
     * 接收者
     */
    @Column("recipient")
    private String recipient;
    
    /**
     * 通知主题
     */
    @Column("subject")
    private String subject;
    
    /**
     * 通知内容
     */
    @Column("content")
    private String content;
    
    /**
     * 发送状态
     */
    @Column("status")
    private NotificationStatus status;
    
    /**
     * 错误信息
     */
    @Column("error_message")
    private String errorMessage;

    /**
     * 发送时间
     */
    @Column("sent_at")
    private LocalDateTime sentAt;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 通知状态枚举（简化版）
     */
    public enum NotificationStatus {
        /**
         * 发送成功
         */
        SUCCESS("SUCCESS", "发送成功"),

        /**
         * 发送失败
         */
        FAILED("FAILED", "发送失败");
        
        private final String code;
        private final String description;
        
        NotificationStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static NotificationStatus fromCode(String code) {
            for (NotificationStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的通知状态: " + code);
        }
    }
    
    /**
     * 创建成功的通知结果
     */
    public static NotificationResult createSuccess(String alarmId, String subscriptionId,
                                              String channel, String recipient,
                                              String subject, String content) {
        LocalDateTime now = LocalDateTime.now();
        return NotificationResult.builder()
                .alarmId(alarmId)
                .subscriptionId(subscriptionId)
                .channel(channel)
                .recipient(recipient)
                .subject(subject)
                .content(content)
                .status(NotificationStatus.SUCCESS)
                .sentAt(now)
                .createdAt(now)
                .build();
    }

    /**
     * 创建失败的通知结果
     */
    public static NotificationResult createFailure(String alarmId, String subscriptionId,
                                              String channel, String recipient,
                                              String subject, String content, String errorMessage) {
        return NotificationResult.builder()
                .alarmId(alarmId)
                .subscriptionId(subscriptionId)
                .channel(channel)
                .recipient(recipient)
                .subject(subject)
                .content(content)
                .status(NotificationStatus.FAILED)
                .errorMessage(errorMessage)
                .createdAt(LocalDateTime.now())
                .build();
    }

    /**
     * 是否发送成功
     */
    public boolean isSuccess() {
        return this.status == NotificationStatus.SUCCESS;
    }

    /**
     * 是否发送失败
     */
    public boolean isFailed() {
        return this.status == NotificationStatus.FAILED;
    }
}
