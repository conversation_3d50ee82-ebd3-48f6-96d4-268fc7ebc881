package com.geeksec.nta.alarm.application.service.query.impl;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.SuppressionListQuery;
import com.geeksec.nta.alarm.application.service.query.AlarmSuppressionQueryService;
import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.domain.repository.AlarmSuppressionRepository;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.infrastructure.converter.AlarmSuppressionConverter;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSuppressionResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 告警抑制查询应用服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AlarmSuppressionQueryServiceImpl implements AlarmSuppressionQueryService {
    
    private final AlarmSuppressionRepository suppressionRepository;
    private final AlarmSuppressionConverter suppressionConverter;
    
    @Override
    public PageResultVo<AlarmSuppressionResponse> querySuppressions(SuppressionListQuery query) {
        log.info("分页查询抑制规则: {}", query);
        
        try {
            // 查询总数
            long total = suppressionRepository.countByQuery(query);
            
            if (total == 0) {
                return new PageResultVo<>(List.of(), 0L, query.getPage(), query.getSize());
            }
            
            // 查询分页数据
            List<AlarmSuppression> suppressions = suppressionRepository.findByQuery(query);
            List<AlarmSuppressionResponse> responses = suppressionConverter.toResponses(suppressions);
            
            return new PageResultVo<>(responses, total, query.getPage(), query.getSize());
                    
        } catch (Exception e) {
            log.error("分页查询抑制规则失败: {}", query, e);
            throw new RuntimeException("查询抑制规则失败", e);
        }
    }
    
    @Override
    public Optional<AlarmSuppressionResponse> getSuppression(SuppressionId suppressionId) {
        log.info("获取抑制规则详情: {}", suppressionId);
        
        try {
            Optional<AlarmSuppression> suppressionOpt = suppressionRepository.findById(suppressionId);
            return suppressionOpt.map(suppressionConverter::toResponse);
        } catch (Exception e) {
            log.error("获取抑制规则详情失败: {}", suppressionId, e);
            throw new RuntimeException("获取抑制规则详情失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppressionResponse> getAllSuppressions() {
        log.info("获取所有抑制规则");
        
        try {
            List<AlarmSuppression> suppressions = suppressionRepository.findAll();
            return suppressionConverter.toResponses(suppressions);
        } catch (Exception e) {
            log.error("获取所有抑制规则失败", e);
            throw new RuntimeException("获取所有抑制规则失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppressionResponse> getActiveSuppressions() {
        log.info("获取活跃的抑制规则");
        
        try {
            List<AlarmSuppression> suppressions = suppressionRepository.findActive();
            return suppressionConverter.toResponses(suppressions);
        } catch (Exception e) {
            log.error("获取活跃抑制规则失败", e);
            throw new RuntimeException("获取活跃抑制规则失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppressionResponse> getSuppressionsByType(String suppressionType) {
        log.info("根据类型获取抑制规则: {}", suppressionType);
        
        try {
            List<AlarmSuppression> suppressions = suppressionRepository.findByType(suppressionType);
            return suppressionConverter.toResponses(suppressions);
        } catch (Exception e) {
            log.error("根据类型获取抑制规则失败: {}", suppressionType, e);
            throw new RuntimeException("根据类型获取抑制规则失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppressionResponse> getSuppressionsByCreator(String creator) {
        log.info("根据创建者获取抑制规则: {}", creator);
        
        try {
            List<AlarmSuppression> suppressions = suppressionRepository.findByCreator(creator);
            return suppressionConverter.toResponses(suppressions);
        } catch (Exception e) {
            log.error("根据创建者获取抑制规则失败: {}", creator, e);
            throw new RuntimeException("根据创建者获取抑制规则失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppressionResponse> getExpiredSuppressions() {
        log.info("获取已过期的抑制规则");
        
        try {
            List<AlarmSuppression> suppressions = suppressionRepository.findExpired(LocalDateTime.now());
            return suppressionConverter.toResponses(suppressions);
        } catch (Exception e) {
            log.error("获取已过期抑制规则失败", e);
            throw new RuntimeException("获取已过期抑制规则失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppressionResponse> getRecentlyUsedSuppressions(int days) {
        log.info("获取最近使用的抑制规则: {} 天内", days);
        
        try {
            LocalDateTime since = LocalDateTime.now().minusDays(days);
            List<AlarmSuppression> suppressions = suppressionRepository.findRecentlyUsed(since);
            return suppressionConverter.toResponses(suppressions);
        } catch (Exception e) {
            log.error("获取最近使用的抑制规则失败: {} 天内", days, e);
            throw new RuntimeException("获取最近使用的抑制规则失败", e);
        }
    }
    
    @Override
    public boolean existsSuppression(SuppressionId suppressionId) {
        log.debug("检查抑制规则是否存在: {}", suppressionId);
        
        try {
            return suppressionRepository.existsById(suppressionId);
        } catch (Exception e) {
            log.error("检查抑制规则是否存在失败: {}", suppressionId, e);
            return false;
        }
    }
    
    @Override
    public boolean isSuppressionNameUsed(String suppressionName, SuppressionId excludeId) {
        log.debug("检查抑制规则名称是否已被使用: name={}, excludeId={}", suppressionName, excludeId);
        
        try {
            return suppressionRepository.existsByNameExcludeId(suppressionName, excludeId);
        } catch (Exception e) {
            log.error("检查抑制规则名称是否已被使用失败: {}", suppressionName, e);
            return false;
        }
    }
    
    @Override
    public long countSuppressions() {
        log.debug("统计抑制规则数量");
        
        try {
            return suppressionRepository.count();
        } catch (Exception e) {
            log.error("统计抑制规则数量失败", e);
            return 0;
        }
    }
    
    @Override
    public long countActiveSuppressions() {
        log.debug("统计活跃抑制规则数量");
        
        try {
            return suppressionRepository.countActive();
        } catch (Exception e) {
            log.error("统计活跃抑制规则数量失败", e);
            return 0;
        }
    }
    
    @Override
    public SuppressionStatistics getSuppressionStatistics() {
        log.info("获取抑制规则统计信息");
        
        try {
            long totalRules = countSuppressions();
            long activeRules = countActiveSuppressions();
            long expiredRules = suppressionRepository.countExpired(LocalDateTime.now());
            long disabledRules = suppressionRepository.countDisabled();
            long recentlyUsedRules = suppressionRepository.countRecentlyUsed(LocalDateTime.now().minusDays(7));
            
            // 这里可以根据需要实现更详细的统计
            List<TopSuppressionRule> topUsedRules = List.of();
            List<SuppressionTrend> trends = List.of();
            
            return new SuppressionStatistics(
                totalRules,
                activeRules,
                expiredRules,
                disabledRules,
                recentlyUsedRules,
                topUsedRules,
                trends
            );
        } catch (Exception e) {
            log.error("获取抑制规则统计信息失败", e);
            throw new RuntimeException("获取抑制规则统计信息失败", e);
        }
    }
}
