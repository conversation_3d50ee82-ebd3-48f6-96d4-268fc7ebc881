<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.infrastructure.mapper.NotificationResultMapper">

    <!-- 分页查询通知记录 -->
    <select id="selectWithPage" resultType="com.geeksec.nta.alarm.domain.aggregate.notification.NotificationResult">
        SELECT * FROM notification_results
        WHERE 1=1
        <if test="subscriptionId != null and subscriptionId != ''">
            AND subscription_id = #{subscriptionId}
        </if>
        <if test="alarmId != null and alarmId != ''">
            AND alarm_id = #{alarmId}
        </if>
        <if test="channel != null and channel != ''">
            AND channel = #{channel}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="startTime != null">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_at &lt;= #{endTime}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 批量插入通知记录 -->
    <insert id="batchInsert">
        INSERT INTO notification_results (
            id, subscription_id, alarm_id, channel, recipient,
            subject, content, status, error_message, sent_at, created_at
        ) VALUES
        <foreach collection="logs" item="log" separator=",">
            (
                #{log.id}, #{log.subscriptionId}, #{log.alarmId}, #{log.channel},
                #{log.recipient}, #{log.subject}, #{log.content},
                #{log.status}, #{log.errorMessage}, #{log.sentAt}, #{log.createdAt}
            )
        </foreach>
    </insert>

    <!-- 查询订阅的通知统计 -->
    <select id="selectStatistics" resultType="com.geeksec.nta.alarm.infrastructure.mapper.NotificationResultMapper$NotificationStatistics">
        SELECT
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failedCount,
            CASE
                WHEN COUNT(*) > 0 THEN
                    ROUND(SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0
            END as successRate
        FROM notification_results
        WHERE subscription_id = #{subscriptionId}
        <if test="startTime != null">
            AND created_at >= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_at &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询失败的通知记录 -->
    <select id="selectFailedNotifications" resultType="com.geeksec.nta.alarm.domain.aggregate.notification.NotificationResult">
        SELECT * FROM notification_results
        WHERE status = 'FAILED'
        ORDER BY created_at ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据告警ID查询通知记录 -->
    <select id="selectByAlarmId" resultType="com.geeksec.nta.alarm.domain.aggregate.notification.NotificationResult">
        SELECT * FROM notification_results
        WHERE alarm_id = #{alarmId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据订阅ID查询通知记录 -->
    <select id="selectBySubscriptionId" resultType="com.geeksec.nta.alarm.domain.aggregate.notification.NotificationResult">
        SELECT * FROM notification_results
        WHERE subscription_id = #{subscriptionId}
        ORDER BY created_at DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 删除过期的通知记录 -->
    <delete id="deleteExpiredLogs">
        DELETE FROM notification_results
        WHERE created_at &lt; #{beforeTime}
    </delete>

    <!-- 查询通知发送成功率 -->
    <select id="selectDailySuccessRate" resultType="com.geeksec.nta.alarm.infrastructure.mapper.NotificationResultMapper$DailySuccessRate">
        SELECT
            DATE(created_at) as date,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) as successCount,
            CASE
                WHEN COUNT(*) > 0 THEN
                    ROUND(SUM(CASE WHEN status = 'SENT' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0
            END as successRate
        FROM notification_results
        WHERE subscription_id = #{subscriptionId}
        AND created_at >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    </select>

</mapper>
