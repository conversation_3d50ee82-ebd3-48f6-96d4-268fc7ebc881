package com.geeksec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.FilterConfigDao;
import com.geeksec.analysis.dao.FilterStateDao;
import com.geeksec.analysis.entity.FilterConfig;
import com.geeksec.analysis.entity.FilterState;
import com.geeksec.analysis.entity.condition.FilterConfigInCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.condition.FilterRuleCondition;
import com.geeksec.analysis.entity.vo.FilterConfigVo;
import com.geeksec.analysis.entity.vo.FilterCsvVo;
import com.geeksec.analysis.entity.vo.FilterInfoVo;
import com.geeksec.analysis.entity.vo.FilterStateVo;
import com.geeksec.analysis.service.FilterRuleService;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.IpUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@DS("nta-db")
public class FilterRuleServiceImpl implements FilterRuleService {

    private static final Logger logger = LoggerFactory.getLogger(FilterRuleServiceImpl.class);

    @Autowired
    private FilterStateDao filterStateDao;
    @Autowired
    private FilterConfigDao filterConfigDao;

    @Override
    public Integer modifyFilterSate(FilterRuleCondition condition) {
        logger.info("修改命中留存/丢弃，condition --->{}", condition);
        return filterStateDao.updateStateByTaskId(condition);
    }

    @Override
    public FilterStateVo getFilterStateByTaskId(Integer taskId) {
        logger.info("获取任务过滤的命中留存/丢弃状态，taskId --->{}", taskId);
        FilterState filterState = filterStateDao.getFilterStateByTaskId(taskId);
        FilterStateVo filterStateVo = new FilterStateVo();
        if (filterState != null) {
            BeanUtils.copyProperties(filterState, filterStateVo);
            return filterStateVo;
        }
        return filterStateVo;

    }

    @Override
    public PageResultVo<FilterConfigVo> getFilterConfigList(FilterRuleCondition condition) {
        logger.info("查询过滤规则集合，condition --->{}", condition);
        PageHelper.startPage(condition.getCurrentPage(), condition.getPageSize());
        List<FilterConfigVo> list = filterConfigDao.getList(condition);
        for (FilterConfigVo filterConfigVo : list) {
            filterConfigVo.setHash(null);
            String filterJson = filterConfigVo.getFilterJson();
            if (StringUtils.isNotBlank(filterJson)) {
                FilterInfoVo filterInfo = JSONObject.parseObject(filterJson, FilterInfoVo.class);
                filterConfigVo.setFilterInfo(filterInfo);
            }
        }
        PageInfo<FilterConfigVo> info = new PageInfo<>(list);
        PageResultVo<FilterConfigVo> result = new PageResultVo<>();
        result.setRecords(list);
        result.setTotal(info.getTotal());
        return result;
    }

    @Override
    public void addConfig(FilterConfigInCondition condition) {
        logger.info("创建过滤规则，filterConfigInCondition --->{}", condition);
        // 首先检测过滤规则的参数是否正确
        filterRuleParamCheck(condition);
        // 确保不存在已经创建同样规则的过滤规则
        checkRuleMD5(condition.getFilterInfo(), condition);

        FilterConfig newConfig = new FilterConfig();
        BeanUtil.copyProperties(condition, newConfig);
        // 设置初始时间
        newConfig.setUpdatedTime((int) (System.currentTimeMillis() / 1000));
        newConfig.setCreatedTime((int) (System.currentTimeMillis() / 1000));
        Integer ruleId = filterConfigDao.insert(newConfig);
        logger.info("过滤规则创建成功,rule_id->{}", ruleId);
    }

    /**
     * 过滤条件参数校验
     *
     * @param filterConfigInCondition
     * @return
     */
    private ResultVo checkParam(FilterConfigInCondition filterConfigInCondition) {
        FilterInfoVo filterInfo = filterConfigInCondition.getFilterInfo();
        if (filterInfo == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        // 校验上传的数据是否符合规范
        Integer taskId = filterConfigInCondition.getTaskId();
        Integer type = filterConfigInCondition.getType();
        //Integer userId = filterConfigInCondition.getUserId();
        if (taskId == null || type == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        try {
            String ip = filterInfo.getIp();
            if (StringUtils.isNotBlank(ip)) {
                filterConfigInCondition.setIp(ip);
                if (!IpUtils.isIpv4Str(ip)) {
                    throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                }
            } else {
                if (type == 2 && StringUtils.isEmpty(ip)) {
                    //网段规则的ip不能为null
                    throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                }
                filterConfigInCondition.setIp("");
                filterInfo.setIp(null);
            }
            String subnetMask = filterInfo.getSubnetMask();
            if (StringUtils.isNotBlank(subnetMask)) {
                // 网关掩码校验
                String[] split = subnetMask.split(".");
                for (String s : split) {
                    Integer integer = Integer.valueOf(s);
                    if (integer < 0 || integer > 255) {
                        throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                    }
                }
            }
            //校验type下的数据合理性
            if (!checkType(type, filterInfo)) {
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
            //删除此type以外的字段
            deleteRedundant(type, filterInfo);
        } catch (Exception e) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        return null;
    }

    /*
       过滤规则参数判定
     */
    private void filterRuleParamCheck(FilterConfigInCondition condition) {
        FilterInfoVo filterInfoVo = condition.getFilterInfo();

        // 预先进行IP与子网掩码的判断
        Integer type = condition.getType();
        String ipAddr = filterInfoVo.getIp();
        if ("all".equals(ipAddr) || (StrUtil.isEmpty(ipAddr) && type != 2)) {
            // 在端口过滤和IP-PRO过滤的情况下，是可以为空的
            logger.info("当前规则按照全量IP进行匹配");
            filterInfoVo.setIp("");
            condition.setIp("");
        } else {
            // 判断其IP地址
            if (type == 2) {
                String subNetMask = filterInfoVo.getSubnetMask();
                if (StrUtil.isEmpty(ipAddr)) {
                    throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
                }
                if (StrUtil.isNotBlank(subNetMask)) {
                    // 网关掩码校验
                    String[] split = subNetMask.split("\\.");
                    for (String s : split) {
                        int integer = Integer.parseInt(s);
                        if (integer < 0 || integer > 255) {
                            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                        }
                    }
                } else {
                    filterInfoVo.setSubnetMask("***************");
                }
            }
            if (!IpUtils.isIpv4Str(ipAddr) && !IpUtils.isIpv6Str(ipAddr)) {
                throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
            }
            filterInfoVo.setIp(ipAddr);
            condition.setIp(ipAddr);
        }

        // 根据不同的type
        switch (type) {
            case 0:
                // 端口范围过滤
                List<Integer> tcpPort = filterInfoVo.getTcpPort();
                List<Integer> udpPort = filterInfoVo.getUdpPort();
                if (CollectionUtil.isEmpty(tcpPort) && CollectionUtil.isEmpty(udpPort)) {
                    throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
                }
                // 根据tcpPort和udpPort的值，进行不同的处理
                if (CollectionUtil.isNotEmpty(tcpPort)) {
                    checkPort(tcpPort, 65535);
                }
                if (CollectionUtil.isNotEmpty(udpPort)) {
                    checkPort(udpPort, 65535);
                }
                break;
            case 1:
                // IP-PRO过滤
                List<Integer> ipPro = filterInfoVo.getIpPro();
                if (CollectionUtil.isEmpty(ipPro)) {
                    throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
                }
                break;
            default:
                break;
        }
        //删除此type以外的字段
        deleteRedundant(type, filterInfoVo);
    }

    private void checkPort(List<Integer> port, Integer limit) {
        if (port != null && !port.isEmpty()) {
            for (Integer k : port) {
                if (k < 0 || k > limit) {
                    throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                }
            }
        }
    }

    private ResultVo checkRuleMD5(FilterInfoVo filterInfo, FilterConfigInCondition filterConfigInCondition) {
        //校验是否和数据库重复
        String jsonString = JSONObject.toJSONString(filterInfo);
        filterConfigInCondition.setFilterJson(jsonString);
        String md5 = DigestUtils.md5DigestAsHex(jsonString.getBytes());
        filterConfigInCondition.setHash(md5);
        FilterConfig oldConfig = filterConfigDao.getOneByHash(filterConfigInCondition);
        if (oldConfig != null) {
            throw new GkException(GkErrorEnum.FILTER_RULE_REPEAT);
        }
        return null;
    }

    @Override
    public ResultVo updateConfig(FilterConfigInCondition condition) {
        condition.setHash(null);
        logger.info("更新过滤规则，filterConfigInCondition --->{}", condition);
        // 首先检测过滤规则的参数是否正确
        filterRuleParamCheck(condition);

        // 确保不存在已经创建同样规则的过滤规则
        checkRuleMD5(condition.getFilterInfo(), condition);
        FilterConfig config = new FilterConfig();
        BeanUtils.copyProperties(condition, config);
        config.setUpdatedTime((int) (new Date().getTime() / 1000));
        filterConfigDao.updateById(config);
        return ResultVo.success();
    }

    @Override
    public ResultVo deleteConfig(FilterDeleteCondition condition) {
        logger.info("删除过滤规则，condition --->{}", condition);
        filterConfigDao.deleteBySome(condition);
        return ResultVo.success();
    }

    @Override
    public ResultVo addConfig(Integer taskId, List<CsvRow> rows) {
        logger.info("csv导入过滤规则开始");
        int i = 0;
        List<Integer> errorList = new ArrayList<>();
        List<Integer> repeatList = new ArrayList<>();
        FilterCsvVo filterCsvVo = new FilterCsvVo();
        filterCsvVo.setErrorList(errorList);
        filterCsvVo.setRepeatList(repeatList);
        filterCsvVo.setTotalNum(rows.size());
        Map<String, FilterConfigInCondition> map = new HashMap<>(rows.size());
        for (CsvRow csvRow : rows) {
            FilterConfigInCondition condition = new FilterConfigInCondition();
            i++;
            condition.setRowNo(i);
            //打印出该行的内容信息
            List<String> rawList = csvRow.getRawList();
            String typeStr = rawList.get(0);
            Integer type;
            try {
                type = Integer.valueOf(typeStr.replaceAll(" ", ""));
                if (type < 0 || type > 2) {
                    errorList.add(i);
                    continue;
                }
            } catch (Exception e) {
                errorList.add(i);
                continue;
            }

            condition.setTaskId(taskId);
            condition.setType(type);
            //组装规则对象
            FilterInfoVo vo;
            try {
                vo = delVo(rawList);
            } catch (Exception e) {
                errorList.add(i);
                continue;
            }
            condition.setFilterInfo(vo);
            String jsonString = JSONObject.toJSONString(vo);
            condition.setFilterJson(jsonString);
            String md5 = DigestUtils.md5DigestAsHex(jsonString.getBytes());
            condition.setHash(md5);
            if (map.containsKey(md5)) {
                //文件内部重复数据
                repeatList.add(i);
            } else {
                map.put(md5, condition);
            }
        }
        FilterRuleCondition queryCondition = new FilterRuleCondition();
        queryCondition.setTaskId(taskId);
        for (String key : map.keySet()) {
            FilterConfigInCondition condition = map.get(key);
            //参数值校验不通过
            ResultVo errorVo = checkParam(condition);
            if (errorVo != null) {
                errorList.add(condition.getRowNo());
                map.remove(key);
            }
        }
        //获取数据库已有的规则
        List<FilterConfigVo> oldList = filterConfigDao.getList(queryCondition);
        if (oldList != null && oldList.size() > 0) {
            for (FilterConfigVo filterConfigVo : oldList) {
                String hash = filterConfigVo.getHash();
                if (map.containsKey(hash)) {
                    //和数据库冲突  记录重复数据 并删除
                    FilterConfigInCondition condition = map.get(hash);
                    repeatList.add(condition.getRowNo());
                    map.remove(hash);
                }
            }
        }
        filterCsvVo.setSucNum(map.size());
        filterCsvVo.setFailNum(filterCsvVo.getTotalNum() - filterCsvVo.getSucNum());
        List<FilterConfig> insertList = new ArrayList<>(1000);
        for (String key : map.keySet()) {
            FilterConfigInCondition condition = map.get(key);
            FilterConfig config = new FilterConfig();
            BeanUtils.copyProperties(condition, config);
            config.setUpdatedTime((int) (new Date().getTime() / 1000));
            config.setCreatedTime((int) (new Date().getTime() / 1000));
            insertList.add(config);
            if (insertList.size() >= 1000) {
                filterConfigDao.addByList(insertList);
            }
        }
        if (insertList.size() > 0) {
            filterConfigDao.addByList(insertList);
        }

        return ResultVo.success(filterCsvVo);
    }

    private FilterInfoVo delVo(List<String> rawList) {
        FilterInfoVo vo = new FilterInfoVo();
        for (String row : rawList) {
            row = row.replaceAll(" ", "");
            //这里认为是k,v
            if (row.contains("&")) {
                String[] split = row.split("&");
                String key = split[0];
                String value = split[1];
                switch (key) {
                    case "ip":
                        vo.setIp(value);
                        break;
                    case "ipPro":
                        //集合
                        String[] ipPro = delStr(value);
                        List<Integer> ipPros = new ArrayList<>(ipPro.length);
                        for (String s : ipPro) {
                            ipPros.add(Integer.valueOf(s));
                        }

                        vo.setIpPro(ipPros);
                        break;
                    case "ipProType":
                        if ("Select".equals(value) || "Invert".equals(value)) {
                            vo.setIpProType(value);
                        } else {
                            vo.setIpProType("Select");
                        }
                        break;
                    case "tcpPort":
                        String[] tcpPort = delStr(value);
                        List<Integer> tcpPorts = Stream.of(tcpPort).map(Integer::valueOf).collect(Collectors.toList());
                        vo.setTcpPort(tcpPorts);
                        break;
                    case "tcpPortType":
                        if ("Select".equals(value) || "Invert".equals(value)) {
                            vo.setTcpPortType(value);
                        } else {
                            vo.setTcpPortType("Select");
                        }
                        break;
                    case "udpPort":
                        String[] udpPort = delStr(value);
                        List<Integer> udpPorts = Stream.of(udpPort).map(Integer::valueOf).collect(Collectors.toList());
                        vo.setUdpPort(udpPorts);
                        break;
                    case "udpPortType":
                        if ("Select".equals(value) || "Invert".equals(value)) {
                            vo.setUdpPortType(value);
                        } else {
                            vo.setUdpPortType("Select");
                        }
                        break;
                    case "subnetMask":
                        vo.setSubnetMask(value);
                        break;
                }
            }
        }
        if (StringUtils.isEmpty(vo.getIp())) {
            vo.setIp("");
        }
        return vo;
    }

    private String[] delStr(String value) {
        value = value.replaceAll("，", ",");
        return value.split(",");
    }

    /**
     * 校验type下的参数是否完整
     */
    private Boolean checkType(Integer type, FilterInfoVo vo) {
        String ip = vo.getIp();
        // 校验IP地址是否正确
        if (ip != null && !IpUtils.isIpv4Str(ip)) {
            return false;
        }
        String select = "Select";
        String invert = "Invert";
        switch (type) {
            case 0:
                //端口
                List<Integer> tcpPort = vo.getTcpPort();
                List<Integer> udpPort = vo.getUdpPort();
                String tcpPortType = vo.getTcpPortType();
                String udpPortType = vo.getUdpPortType();
                if (tcpPort == null) {
                    tcpPort = new ArrayList<>();
                    vo.setTcpPort(tcpPort);
                }
                if (udpPort == null) {
                    udpPort = new ArrayList<>();
                    vo.setUdpPort(udpPort);
                }
                if (StringUtils.isEmpty(tcpPortType) || StringUtils.isEmpty(udpPortType)) {
                    return false;
                }

                if (!(tcpPortType.equals(select) || tcpPortType.equals(invert))) {
                    return false;
                }
                if (!(udpPortType.equals(select) || udpPortType.equals(invert))) {
                    return false;
                }
                break;
            case 1:
                List<Integer> ipPro = vo.getIpPro();
                String ipProType = vo.getIpProType();
                if (ipPro == null) {
                    ipPro = new ArrayList<>();
                    vo.setIpPro(ipPro);
                }
                if (!(ipProType.equals(select) || ipProType.equals(invert))) {
                    return false;
                }
                break;
            case 2:
                if (StringUtils.isEmpty(vo.getSubnetMask())) {
                    vo.setSubnetMask("***************");
                }
                break;
        }
        return true;
    }

    /**
     * 删除多余字段
     */
    private void deleteRedundant(Integer type, FilterInfoVo vo) {
        switch (type) {
            case 0:
                vo.setSubnetMask(null);
                vo.setIpPro(null);
                vo.setIpProType(null);
                break;
            case 1:
                vo.setTcpPortType(null);
                vo.setTcpPort(null);
                vo.setUdpPort(null);
                vo.setUdpPortType(null);
                vo.setSubnetMask(null);
                break;
            case 2:
                vo.setTcpPortType(null);
                vo.setTcpPort(null);
                vo.setUdpPort(null);
                vo.setUdpPortType(null);
                vo.setIpPro(null);
                vo.setIpProType(null);
                break;
        }
    }

    @Override
    public List<List<String>> getListByTaskId(Integer taskId) {
        QueryWrapper<FilterConfig> queryWrapper = new QueryWrapper();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.eq("status", Constants.ON);
        List<FilterConfig> filterConfigs = filterConfigDao.selectList(queryWrapper);
        List<List<String>> rows = new ArrayList<>(filterConfigs.size());
        for (FilterConfig config : filterConfigs) {
            List<String> names = new ArrayList<>();
            names.add(config.getType().toString());
            names.add("ip&" + config.getIp());
            String filterJson = config.getFilterJson();
            FilterInfoVo info = JSONObject.parseObject(filterJson, FilterInfoVo.class);
            List<Integer> ipPro = info.getIpPro();
            if (ipPro != null && ipPro.size() > 0) {
                names.add("ipPro&" + StringUtils.join(ipPro, ","));
                names.add("ipProType&" + info.getIpProType());
            }
            List<Integer> tcpPort = info.getTcpPort();
            if (tcpPort != null && tcpPort.size() > 0) {
                names.add("tcpPort&" + StringUtils.join(tcpPort, ","));
                names.add("tcpPortType&" + info.getTcpPortType());
            }
            List<Integer> udpPort = info.getUdpPort();
            if (udpPort != null && udpPort.size() > 0) {
                names.add("udpPort&" + StringUtils.join(udpPort, ","));
                names.add("udpPortType&" + info.getUdpPortType());
            }
            String subnetMask = info.getSubnetMask();
            if (StringUtils.isNotBlank(subnetMask)) {
                names.add("subnetMask&" + subnetMask);
            }
            rows.add(names);
        }
        return rows;
    }

    @Override
    public List<List<String>> getCsv(Integer taskId) {
        QueryWrapper<FilterConfig> queryWrapper = new QueryWrapper();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.eq("status", Constants.ON);
        List<FilterConfig> filterConfigs = filterConfigDao.selectList(queryWrapper);
        List<List<String>> rows = new ArrayList<>(filterConfigs.size());
        List<String> title = new ArrayList<>();
        title.add("任务id");
        title.add("规则type");
        title.add("ip");
        title.add("json");
        rows.add(title);
        for (FilterConfig config : filterConfigs) {
            List<String> names = new ArrayList<>();
            names.add(config.getTaskId().toString());
            names.add(config.getType().toString());
            names.add(config.getIp());
            names.add(config.getFilterJson());
            rows.add(names);
        }
        return rows;
    }

    @Override
    public ResultVo<FilterCsvVo> addConfigByCSV(Integer taskId, List<CsvRow> rows) {
        List<Integer> error = new ArrayList<>();
        List<Integer> repeat = new ArrayList<>();
        int i = 0;
        Map<String, FilterConfigInCondition> map = new HashMap<>();
        //组装插入对象
        boolean isTitle = false;
        for (CsvRow row : rows) {
            try {
                i++;
                List<String> rawList = row.getRawList();
                if ("任务id".equals(rawList.get(0))) {
                    //去除title 表示有title
                    isTitle = true;
                    continue;
                }
                FilterConfigInCondition condition = new FilterConfigInCondition();
                String type = rawList.get(1);
                String ip = rawList.get(2);
                String json = rawList.get(3);
                condition.setTaskId(taskId);
                condition.setType(Integer.valueOf(type));
                condition.setFilterJson(json);
                condition.setFilterInfo(JSON.parseObject(json, FilterInfoVo.class));
                ResultVo errorVo = checkParam(condition);
                if (errorVo != null) {
                    //记录异常
                    error.add(i);
                    continue;
                }
                String md5 = DigestUtils.md5DigestAsHex(json.getBytes());
                condition.setHash(md5);
                //这里涉及到
                if (map.containsKey(md5)) {
                    repeat.add(i);
                } else {
                    map.put(md5, condition);
                }
            } catch (Exception e) {
                //记录异常
                error.add(i);
            }
        }
        //获取数据库已有的规则
        FilterRuleCondition queryCondition = new FilterRuleCondition();
        queryCondition.setTaskId(taskId);
        List<FilterConfigVo> oldList = filterConfigDao.getList(queryCondition);
        if (oldList != null && oldList.size() > 0) {
            for (FilterConfigVo filterConfigVo : oldList) {
                String hash = filterConfigVo.getHash();
                if (map.containsKey(hash)) {
                    //和数据库冲突  记录重复数据 并删除
                    FilterConfigInCondition condition = map.get(hash);
                    repeat.add(condition.getRowNo());
                    map.remove(hash);
                }
            }
        }
        FilterCsvVo result = new FilterCsvVo();
        if (isTitle) {
            //如果有标题
            result.setTotalNum(rows.size() - 1);
        } else {
            result.setTotalNum(rows.size());
        }
        result.setSucNum(map.size());
        result.setRepeatList(repeat);
        result.setFailNum(result.getTotalNum() - result.getSucNum());
        result.setErrorList(error);
        List<FilterConfig> insertList = new ArrayList<>(1000);
        for (String key : map.keySet()) {
            FilterConfig config = new FilterConfig();
            FilterConfigInCondition condition = map.get(key);
            BeanUtils.copyProperties(condition, config);
            config.setUpdatedTime((int) (new Date().getTime() / 1000));
            config.setCreatedTime((int) (new Date().getTime() / 1000));
            insertList.add(config);
            if (insertList.size() >= 1000) {
                filterConfigDao.addByList(insertList);
                insertList.clear();
            }
        }
        if (insertList.size() > 0) {
            filterConfigDao.addByList(insertList);
        }

        return ResultVo.success(result);
    }
}
