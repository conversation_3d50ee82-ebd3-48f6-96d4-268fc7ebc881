package com.geeksec.general.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.*;
import com.geeksec.analysis.entity.CertTagInfoEntity;
import com.geeksec.analysis.entity.FeatureRule;
import com.geeksec.analysis.entity.IpProtocol;
import com.geeksec.analysis.entity.ModelAttackInfo;
import com.geeksec.analysis.entity.dict.*;
import com.geeksec.analysis.entity.vo.TagLibraryVo;
import com.geeksec.general.service.AppDictService;
import org.apache.hbase.thirdparty.io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class AppDictServiceImpl implements AppDictService {

    private final static Logger logger = LoggerFactory.getLogger(AppDictServiceImpl.class);

    @Value("${enabled.atlas}")
    private Boolean hasAtlas;

    // 产品ID 0.全部 1.探针 2.分析平台 3.其他
    @Value("${shield_pro_type}")
    private Integer shieldProType;

    @Autowired
    private FeatureRuleDao featureRuleDao;

    @Autowired(required = false)
    private ThSessionDao thSessionDao;

    @Autowired
    private ThAnalysisDao thAnalysisDao;

    @Autowired
    private CertTagInfoDao certTagInfoDao;

    @Autowired
    private IpProtocolDao ipProtocolDao;

    @Autowired
    private KnowledgeAlarmDao knowledgeAlarmDao;

    private final static Map<String, String> CERT_TYPE_MAP = new LinkedHashMap<String, String>() {{
        put("0", "白名单");
        put("1", "黑名单");
    }};

    private final static Map<String, String> DOMAIN_TYPE_MAP = new LinkedHashMap<String, String>() {{
        put("1", "精确域名");
        put("2", "模糊域名");
    }};

    private final static Map<String, String> CERT_SITUATION_TYPE_MAP = new LinkedHashMap<String, String>() {{
        put("0", "证书数量");
        put("1", "用户导入证书标签");
        put("2", "证书算法和密钥长度数量分布");
        put("3", "基于签发机构的证书分布");
        put("4", "基于黑白名单权值的证书分布");

    }};

    private final static Map<String, String> ALARM_TYPE_MAP = new LinkedHashMap<String, String>() {{
        put("0", "IP");
        put("1", "端口");
        put("2", "应用");
        put("3", "域名");
        put("4", "证书");
        put("5", "MAC");
        put("6", "连接");
    }};

    private final static Map<String, String> ALARM_TARGET_TYPE_MAP = new LinkedHashMap<String, String>() {{
        put("0", "IP");
        put("1", "端口");
        put("2", "应用");
        put("3", "域名");
        put("4", "证书");
        put("5", "MAC");
        put("6", "连接");
        put("7", "指纹");
    }};

    private final static Map<String, String> ALARM_STATE_MAP = new LinkedHashMap<String, String>() {{
        put("0", "未处理");
        put("1", "已处理");
    }};

    private final static Map<String, String> ALARM_NATURE_MAP = new LinkedHashMap<String, String>() {{
        put("0", "-");
        put("1", "攻击者");
        put("2", "被攻击者");
        put("4", "被控主机");
    }};

    private final static List<String> MODEL_ATTACK_CHAIN_LSIT = new ArrayList<String>() {{
        add("侦查探测");
        add("漏洞利用");
        add("安装植入");
        add("命令控制");
        add("目标行动");
        add("其他");
    }};

    private final static Map<String, String> ALARM_TARGET_BELONG_MAP = new LinkedHashMap<String, String>() {{
        put("0", "外部目标");
        put("1", "内部目标");
    }};

    private final static Map<Integer, String> TAG_TARGET_TYPE_TO_CATEGORY = new HashMap<Integer, String>() {{
        put(0, "IP");
        put(1, "端口");
        put(2, "应用");
        put(3, "域名");
        put(4, "证书");
        put(5, "MAC");
        put(6, "会话");
        put(7, "指纹");
    }};


    @Override
    public Map<String, Object> getAppDict() throws IOException {
        Map<String, Object> appDictMap = new HashMap<>();

        appDictMap.put("cert_type", CERT_TYPE_MAP);
        appDictMap.put("capture_mode", getCaptrueModeMap());
        appDictMap.put("domain_type", DOMAIN_TYPE_MAP);
        appDictMap.put("cert_situation_type", CERT_SITUATION_TYPE_MAP);
        appDictMap.put("cert_sign", getCertTagMap());
        appDictMap.put("analysis_sign", getTbTagInfo());
        appDictMap.put("alarm_type", ALARM_TYPE_MAP);
        appDictMap.put("alarm_nature", ALARM_NATURE_MAP);
        appDictMap.put("alarm_state", ALARM_STATE_MAP);
        appDictMap.put("alarm_target_belong", ALARM_TARGET_BELONG_MAP);
        appDictMap.put("alarm_target_type", ALARM_TARGET_TYPE_MAP);
        appDictMap.put("alarm_model_attack_chain", getAttackChainMap());
        appDictMap.put("es_query_field", getEsQueryField());
        appDictMap.put("download_search_field", getDownloadSearchField());

        appDictMap.put("protocol_type", getProtocolMap());
        appDictMap.put("nebula_type", getNebulaTypeMap());
        appDictMap.put("tag_edge_relation", getTagEdgeRelation());

        List<AppProValue> appDict = thAnalysisDao.getAppProValueDict();
        Map<Integer, String> appIdMap = new HashMap<>();
        Map<Integer, String> appValueMap = new HashMap<>();
        Map<Integer, String> appTypeMap = new HashMap<>();
        for (AppProValue apv : appDict) {
            appIdMap.put(apv.getProId(), apv.getProName());
            appValueMap.put(apv.getProId(), apv.getProValue());
            appTypeMap.put(apv.getProId(), apv.getType().toString());
        }
        appDictMap.put("app_id", appIdMap);
        appDictMap.put("app_value", appValueMap);
        appDictMap.put("app_type_map", appTypeMap);

        List<AppProValue> appRuleDict = thAnalysisDao.getAppRuleDict();
        Map<Integer, String> appIdRuleMap = new HashMap<>();
        Map<Integer, String> appValueRuleMap = new HashMap<>();
        for (AppProValue apv : appRuleDict) {
            appIdRuleMap.put(apv.getProId(), apv.getProName());
            appValueRuleMap.put(apv.getProId(), apv.getProValue() + "（" + apv.getProName() + "）");
        }
        appDictMap.put("app_id_rule", appIdRuleMap);
        appDictMap.put("app_value_rule", appValueRuleMap);

        appDictMap.put("init_time", 0);
        appDictMap.put("protocol_metadata", getProtocolMetadata());
        appDictMap.put("tag_category", getTagCategory2());
        appDictMap.put("all_tag_category", getAllTagCategory());

        appDictMap.put("country", getCountry());

        //nebula属性字典
        appDictMap.put("nebula_properties", getPropertiesMap());

        return appDictMap;
    }

    @Override
    public List<Map<String, Object>> getAlarmDict(){
        return getAttackChainMap();
    }

    /**
     * 获取可查询元数据ES字段字典
     *
     * @return
     */
    private Object getEsQueryField() {
        String jsonStr = StringUtil.EMPTY_STRING;
        try {
            jsonStr = parseFile("dict/new_field_dict.json");
            JSONArray jsonArray = JSON.parseArray(jsonStr);
            return jsonArray;
            //Map<String,Object> result = JSON.parseObject(jsonStr, Map.class);
            //return result;
        } catch (Exception e) {
            logger.error("读取es_query_field.json文件失败", e);
            return null;
        }
    }

    /**
     * 获取下载列表查询条件字段字典
     *
     * @return
     */
    private Object getDownloadSearchField() {
        String jsonStr = StringUtil.EMPTY_STRING;
        try {
            jsonStr = parseFile("dict/download_search_field.json");
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            return jsonObject;
            //Map<String,Object> result = JSON.parseObject(jsonStr, Map.class);
            //return result;
        } catch (Exception e) {
            logger.error("读取es_query_field.json文件失败", e);
            return null;
        }
    }

    private List<Map<String, Object>> getAttackChainMap() {
        // 查询结果列表
        List<ModelAttackInfo> attackInfoList = knowledgeAlarmDao.getAttackChainMap();

        // 定义Map 存储分组统计信息,先生成大的分组
        Map<String, Map<String, Object>> groupStatMap = new LinkedHashMap<>();

        // 外层字典生成
        for (ModelAttackInfo attackInfo : attackInfoList) {
            String alarmName = attackInfo.getAlarmName();
            String typeName = attackInfo.getAttackTypeName();

            // 如果当前攻击链类型没有出现过，创建新的攻击链分组
            if (!groupStatMap.containsKey(typeName)) {
                Map<String, Object> groupStat = new LinkedHashMap<>();
                groupStat.put("attack_chain_name", typeName);
                groupStat.put("attack_chain_cnt", 0);
                groupStat.put("alarm_knowledge_list", new ArrayList<Map<String, Object>>());
                groupStatMap.put(typeName, groupStat);
            }

            Map<String, Object> groupStat = groupStatMap.get(typeName); // 拿到当前这层的map组合信息
            List<Map<String, Object>> alarmKnowledgeList = (List<Map<String, Object>>) groupStat.get("alarm_knowledge_list");
            // 如果当前告警知识库还没有出现过，则创建新的告警知识分组
            Optional<Map<String, Object>> optional = alarmKnowledgeList.stream()
                    .filter(a -> a.get("alarm_knowledge_name").equals(alarmName))
                    .findFirst();
            if (!optional.isPresent()) {
                Map<String, Object> alarmKnowledgeStat = new LinkedHashMap<>();
                alarmKnowledgeStat.put("alarm_knowledge_id", attackInfo.getKnowledgeAlarmId());
                alarmKnowledgeStat.put("alarm_knowledge_name", alarmName);
                alarmKnowledgeStat.put("alarm_knowledge_cnt", 0);
                alarmKnowledgeList.add(alarmKnowledgeStat);
            }
        }

        // 生成出对应的Map后，遍历所有的value值生成新的List
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> groupStat : groupStatMap.values()) {
            resultList.add(groupStat);
        }

        return resultList;
    }

    private Map<String, Map<String, Object>> getTagEdgeRelation() throws IOException {
        try {
            String jsonStr = parseFile(hasAtlas ? "dict/atlas/parse_conf.json" : "dict/analysis/parse_conf.json");
            List<Map<String, Object>> relationList = JSON.parseObject(jsonStr, new TypeReference<List<Map<String, Object>>>() {});
            Map<String, Map<String, Object>> resultMap = new HashMap<>();
            for (Map<String, Object> data : relationList) {
                List<String> handleList = ((List<String>) data.get("handle")).stream()
                        .map(currStr -> "f-" + currStr)
                        .collect(Collectors.toList());

                List<String> rhandleList = ((List<String>) data.get("Rhandle")).stream()
                        .map(currStr -> "r-" + currStr)
                        .collect(Collectors.toList());

                List<String> allHandleList = Stream.concat(handleList.stream(), rhandleList.stream())
                        .collect(Collectors.toList());

                Map<String, Object> handleMap = new HashMap<>();
                handleMap.put("all", allHandleList);

                resultMap.put((String) data.get("type"), handleMap);
            }

            return resultMap;
        } catch (Exception e) {
            logger.error("获取点边对照关系字典失败,error->{}", e);
        }

        return Collections.emptyMap();
    }

    private Map<String, JSONObject> getNebulaTypeMap() {
        try {
            String jsonStr = StringUtil.EMPTY_STRING;
            if (hasAtlas) {
                jsonStr = parseFile("dict/atlas/nebula_dict.json");
            } else {
                jsonStr = parseFile("dict/analysis/nebula_dict.json");
            }

            Map<String, JSONObject> result = (Map<String, JSONObject>) JSON.parse(jsonStr);
            Map<String, JSONObject> addResult = new HashMap<>();
            for (String key : result.keySet()) {
                //拼接s_  和  d_的对象
                JSONObject jsonObject = result.get(key);
                String type = jsonObject.getString("type");
                String desc = jsonObject.getString("desc");
                if ("vertex".equals(type)) {
                    //创建 客户端 对象
                    JSONObject sJson = new JSONObject();
                    String sKey = "s_" + key;
                    sJson.put("type", type);
                    sJson.put("desc", "客户端" + desc);
                    addResult.put(sKey, sJson);
                    //创建 客户端 对象
                    JSONObject dJson = new JSONObject();
                    String dKey = "d_" + key;
                    dJson.put("type", type);
                    dJson.put("desc", "服务端" + desc);
                    addResult.put(dKey, dJson);
                }
            }
            result.putAll(addResult);
            return result;
        } catch (Exception e) {
            logger.error("读取Nebula_dict.json文件失败,error-->", e);
            return new HashMap<>();
        }
    }

    private List<TagLibraryVo> getAllTagCategory() {
        // 标签查询
        List<TagLibraryVo> tagList = new ArrayList<>();
        List<FeatureRule> ruleList = new ArrayList<>();
        try {
            tagList = thSessionDao.listAllTagAttribute();
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("status", 1);
            ruleList = featureRuleDao.selectList(queryWrapper);
        } catch (Exception e) {
            return new ArrayList<>();
        }

        // rule to tag
        for (FeatureRule rule : ruleList) {
            TagLibraryVo tag = new TagLibraryVo();
            tag.setAttributeName(rule.getRuleState());
            tag.setBlackList(rule.getRuleLevel());
            tag.setTagExplain(rule.getRuleDesc());
            tag.setTagId(rule.getRuleId());
            tag.setTagTargetType(6);
            tag.setWhiteList(0);
            tag.setTagText("规则_" + rule.getRuleName());

            tagList.add(tag);
        }

        tagList = handleTagLevel(tagList);
        return tagList;
    }

    private List<Object> getTagCategory() {

        List<Object> allTags = new ArrayList<>();

        Map<Integer, AnalysisTagInfoEntity> mID2Tag = new HashMap<>();
        List<AnalysisTagInfoEntity> recordsFromTag = thAnalysisDao.getAllTagInfo();
        List<RuleEntity> recordsFromRule = thAnalysisDao.getRule();

        for (AnalysisTagInfoEntity tagInfo : recordsFromTag) {
            mID2Tag.put(tagInfo.getTagId(), tagInfo);
        }

        List<TagAttribute> viewTagAttribute = thAnalysisDao.getTagAttribute();

        for (TagAttribute value : viewTagAttribute) {
            String[] sliId = value.getTagStr().split(",");
            for (String tagStr : sliId) {
                Integer id = Integer.valueOf(tagStr);
                AnalysisTagInfoEntity tagInfo = mID2Tag.get(id);
                if (ObjectUtils.isEmpty(tagInfo)) {
                    continue;
                }
                Map<String, Object> allTagMap = new HashMap<>();
                allTagMap.put("value", tagInfo.getTagText());
                allTagMap.put("tag_explain", tagInfo.getTagExplain());
                allTagMap.put("id", tagInfo.getTagId());
                allTagMap.put("black_list", tagInfo.getBlackList());
                allTagMap.put("white_list", tagInfo.getWhiteList());
                allTagMap.put("type", tagInfo.getTagTargetType());
                allTagMap.put("category", value.getAttributeName());
                allTags.add(allTagMap);

            }
        }

        for (AnalysisTagInfoEntity tagInfo : recordsFromTag) {
            if (tagInfo.getTagTargetType() != 9999) {
                continue;
            }
            Set<Integer> keys = TAG_TARGET_TYPE_TO_CATEGORY.keySet();
            Iterator<Integer> iterator = keys.iterator();
            while (iterator.hasNext()) {
                Integer key = iterator.next();
                Map<String, Object> allTagMap = new HashMap<>();
                allTagMap.put("value", tagInfo.getTagText());
                allTagMap.put("tag_explain", tagInfo.getTagExplain());
                allTagMap.put("id", tagInfo.getTagId());
                allTagMap.put("black_list", tagInfo.getBlackList());
                allTagMap.put("white_list", tagInfo.getWhiteList());
                allTagMap.put("type", tagInfo.getTagTargetType());
                allTagMap.put("category", TAG_TARGET_TYPE_TO_CATEGORY.get(key));
                allTags.add(allTagMap);
            }
        }

        for (AnalysisTagInfoEntity tagInfo : recordsFromTag) {
            if (tagInfo.getTagTargetType() != 9999) {
                continue;
            }
            Map<String, Object> allTagMap = new HashMap<>();
            allTagMap.put("value", tagInfo.getTagText());
            allTagMap.put("tag_explain", tagInfo.getTagExplain());
            allTagMap.put("id", tagInfo.getTagId());
            allTagMap.put("black_list", tagInfo.getBlackList());
            allTagMap.put("white_list", tagInfo.getWhiteList());
            allTagMap.put("type", tagInfo.getTagTargetType());
            allTagMap.put("category", TAG_TARGET_TYPE_TO_CATEGORY.get(tagInfo.getTagTargetType()));
            allTags.add(allTagMap);
        }

        for (RuleEntity rule : recordsFromRule) {
            Map<String, Object> ruleTagMap = new HashMap<>();
            ruleTagMap.put("value", "规则_" + rule.getRuleName());
            ruleTagMap.put("tag_explain", rule.getRuleName());
            ruleTagMap.put("id", rule.getRuleId());
            ruleTagMap.put("black_list", rule.getRuleLevel());
            ruleTagMap.put("white_list", 0);
            ruleTagMap.put("type", 6);
            ruleTagMap.put("category", TAG_TARGET_TYPE_TO_CATEGORY.get(6));
            allTags.add(ruleTagMap);
        }
        return allTags;
    }

    private List<TagLibraryVo> getTagCategory2() {
        // 标签查询
        List<TagLibraryVo> tagList = new ArrayList<>();
        List<FeatureRule> ruleList = new ArrayList<>();
        try {
            tagList = thSessionDao.listTagAttribute(shieldProType);
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("status", 1);
            ruleList = featureRuleDao.selectList(queryWrapper);
        } catch (Exception e) {
            return new ArrayList<>();
        }

        // rule to tag
        for (FeatureRule rule : ruleList) {
            TagLibraryVo tag = new TagLibraryVo();
            tag.setAttributeName(rule.getRuleState());
            tag.setBlackList(rule.getRuleLevel());
            tag.setTagExplain(rule.getRuleDesc());
            tag.setTagId(rule.getRuleId());
            tag.setTagTargetType(6);
            tag.setWhiteList(0);
            tag.setTagText("规则_" + rule.getRuleName());

            tagList.add(tag);
        }
        return tagList;
    }

    private Map<String, Object> getProtocolMap() {
        QueryWrapper<IpProtocol> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("pro_id");
        List<IpProtocol> ipProtocolList = ipProtocolDao.selectList(queryWrapper);
        return ipProtocolList.stream()
                .collect(Collectors.toMap(
                        IpProtocol::getProId,
                        i -> {
                            Map<String, String> innerMap = new HashMap<>();
                            innerMap.put("protocol_remark", i.getProtocolRemark());
                            innerMap.put("protocol_type", i.getProtocolType());
                            return innerMap;
                        }
                ));
    }

    private Map<String, Object> getProtocolMetadata() {
        try {
            String jsonStr = StringUtil.EMPTY_STRING;
            jsonStr = parseFile("dict/protocol_metadata.json");
            Map<String, Object> result = (Map<String, Object>) JSON.parse(jsonStr);
            return result;
        } catch (Exception e) {
            logger.error("读取protocol_metadata.json文件失败,error--->", e.getMessage());
            return null;
        }
    }


    private Map<String, Map<String, Object>> getCertTagMap() {

        QueryWrapper<CertTagInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tag_target_type", 4);
        List<CertTagInfoEntity> tagList = certTagInfoDao.selectList(queryWrapper);
        Map<String, Map<String, Object>> resultMap = new HashMap();

        for (CertTagInfoEntity certTag : tagList) {
            Map<String, Object> certMap = new HashMap<>();
            certMap.put("id", certTag.getTagId());
            certMap.put("value", certTag.getTagText());
            certMap.put("black_list", certTag.getBlackList());
            certMap.put("white_list", certTag.getWhiteList());
            resultMap.put(certTag.getTagText(), certMap);
        }

        return resultMap;
    }

    public Map<String, String> getCaptrueModeMap() {

        List<ValSetEntity> list = thAnalysisDao.getCaptureMode();
        Map<String, String> captureModeMap = new HashMap<>();
        for (ValSetEntity valSet : list) {
            captureModeMap.put(valSet.getValId(), valSet.getValue());
        }

        return captureModeMap;
    }

    private Map<String, Object> getTbTagInfo() {

        List<AnalysisTagInfoEntity> recordsFromTag = thAnalysisDao.getTagInfo(shieldProType);
        List<RuleEntity> recordsFromRule = thAnalysisDao.getRule();

        Map<String, Object> result = new HashMap<>();
        for (AnalysisTagInfoEntity tagInfo : recordsFromTag) {
            Map<String, Object> tagInfoMap = new HashMap<>();
            tagInfoMap.put("value", tagInfo.getTagText());
            tagInfoMap.put("tag_explain", tagInfo.getTagExplain());
            tagInfoMap.put("id", tagInfo.getTagId());
            tagInfoMap.put("black_list", tagInfo.getBlackList());
            tagInfoMap.put("white_list", tagInfo.getWhiteList());
            tagInfoMap.put("type", tagInfo.getTagTargetType());
            result.put(tagInfo.getTagRemark(), tagInfoMap);
        }

        for (RuleEntity ruleInfo : recordsFromRule) {
            Map<String, Object> ruleMap = new HashMap<>();
            ruleMap.put("value", "规则_" + ruleInfo.getRuleName().toString());
            ruleMap.put("tag_explain", ruleInfo.getRuleName());
            ruleMap.put("id", ruleInfo.getRuleId());
            ruleMap.put("black_list", ruleInfo.getRuleLevel());
            ruleMap.put("white_list", 0);
            ruleMap.put("type", 6);
            result.put(ruleInfo.getRuleName(), ruleMap);
        }

        return result;
    }

    private String parseFile(String path) throws IOException {

        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
            return stringBuilder.toString();
        } catch (IOException e) {
            logger.error("读取path->{}文件失败！error->{}", path, e);
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        return null;
    }

    private List<TagLibraryVo> handleTagLevel(List<TagLibraryVo> tagList) {
        for (TagLibraryVo info : tagList) {
            int blackList = info.getBlackList();
            int whiteList = info.getWhiteList();
            if (blackList >= 1 && blackList <= 100) {
                if (whiteList != 100) {
                    if (blackList >= 80) {
                        info.setTagLevel("danger");
                    } else {
                        info.setTagLevel("warning");
                    }
                } else {
                    info.setTagLevel("positive");
                }
            } else if (whiteList >= 1 && whiteList <= 100) {
                if (whiteList == 100) {
                    info.setTagLevel("success");
                } else {
                    info.setTagLevel("positive");
                }
            } else {
                info.setTagLevel("info");
            }
        }
        return tagList;
    }


    private Map<String, Object> getCountry() {
        try {
            String jsonStr = StringUtil.EMPTY_STRING;
            jsonStr = parseFile("dict/country.json");
            Map<String, Object> result = (Map<String, Object>) JSON.parse(jsonStr);
            return result;
        } catch (Exception e) {
            logger.error("读取country.json文件失败");
            return null;
        }
    }

    private List<Map<String, Object>> getPropertiesMap() {

        try {
            String jsonStr = io.netty.util.internal.StringUtil.EMPTY_STRING;
            jsonStr = parseFile("dict/analysis/nebula_properties.json");
            List<Map<String, Object>> list = (List<Map<String, Object>>) JSON.parse(jsonStr);
            return list;
        } catch (Exception e) {
            logger.error("读取nebula_properties.json文件失败,error--->", e.getMessage());
            return null;
        }
    }
}
