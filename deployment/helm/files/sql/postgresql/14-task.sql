-- ========================================
-- NTA 3.0 任务管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 任务管理模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 核心任务管理表
-- ========================================

-- 任务分析表
DROP TABLE IF EXISTS task_analysis CASCADE;

CREATE TABLE task_analysis (
    id SERIAL PRIMARY KEY,
    created_by INTEGER DEFAULT 1,
    task_id INTEGER,
    task_name TEXT NOT NULL,
    task_remark TEXT,
    task_state VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type INTEGER,
    task_status INTEGER DEFAULT 1
);

COMMENT ON TABLE task_analysis IS '任务分析表';
COMMENT ON COLUMN task_analysis.created_by IS '创建者ID';
COMMENT ON COLUMN task_analysis.task_id IS '任务ID';
COMMENT ON COLUMN task_analysis.task_name IS '任务名称';
COMMENT ON COLUMN task_analysis.task_remark IS '任务备注';
COMMENT ON COLUMN task_analysis.task_state IS '任务状态';
COMMENT ON COLUMN task_analysis.created_at IS '创建时间';
COMMENT ON COLUMN task_analysis.updated_at IS '更新时间';
COMMENT ON COLUMN task_analysis.task_type IS '任务类型';
COMMENT ON COLUMN task_analysis.task_status IS '任务状态标识';

-- 任务批次表
DROP TABLE IF EXISTS task_batch CASCADE;

CREATE TABLE task_batch (
    batch_id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    batch_remark TEXT,
    fullflow_state VARCHAR(3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    batch_status INTEGER DEFAULT 1
);

COMMENT ON TABLE task_batch IS '任务批次表';
COMMENT ON COLUMN task_batch.batch_id IS '自增批次ID';
COMMENT ON COLUMN task_batch.task_id IS '任务ID';
COMMENT ON COLUMN task_batch.batch_remark IS '批次描述';
COMMENT ON COLUMN task_batch.fullflow_state IS '全流量留存，ON启用，OFF停用';
COMMENT ON COLUMN task_batch.created_at IS '创建时间';
COMMENT ON COLUMN task_batch.updated_at IS '更新时间';
COMMENT ON COLUMN task_batch.batch_status IS '批次状态';

-- 任务批次插件关联表
DROP TABLE IF EXISTS task_batch_plugin CASCADE;

CREATE TABLE task_batch_plugin (
    id SERIAL PRIMARY KEY,
    batch_id INTEGER NOT NULL,
    plugin_id INTEGER NOT NULL,
    should_log_def INTEGER NOT NULL
);

COMMENT ON TABLE task_batch_plugin IS '任务批次插件关联表';
COMMENT ON COLUMN task_batch_plugin.batch_id IS '批次ID';
COMMENT ON COLUMN task_batch_plugin.plugin_id IS '插件ID';
COMMENT ON COLUMN task_batch_plugin.should_log_def IS '开启的插件ID默认值，1为开启，0为关闭';

-- 任务内部IP配置表
DROP TABLE IF EXISTS task_internal_ip CASCADE;

CREATE TABLE task_internal_ip (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    netmask VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE task_internal_ip IS '任务内部IP配置表';
COMMENT ON COLUMN task_internal_ip.task_id IS '任务ID';
COMMENT ON COLUMN task_internal_ip.ip IS 'IP网段信息';
COMMENT ON COLUMN task_internal_ip.netmask IS '网段掩码';
COMMENT ON COLUMN task_internal_ip.created_at IS '创建时间';
COMMENT ON COLUMN task_internal_ip.updated_at IS '更新时间';

-- 离线任务批次文件表
DROP TABLE IF EXISTS offline_task_batch_file CASCADE;

CREATE TABLE offline_task_batch_file (
    id SERIAL PRIMARY KEY,
    task_id BIGINT,
    batch_id BIGINT,
    batch_type INTEGER DEFAULT 0,
    file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE offline_task_batch_file IS '离线任务批次文件表';
COMMENT ON COLUMN offline_task_batch_file.task_id IS '任务ID';
COMMENT ON COLUMN offline_task_batch_file.batch_id IS '批次ID';
COMMENT ON COLUMN offline_task_batch_file.batch_type IS '批次类型（1-服务器数据；2-数据上传）';
COMMENT ON COLUMN offline_task_batch_file.file_path IS '文件路径';
COMMENT ON COLUMN offline_task_batch_file.created_at IS '创建时间';

-- 批次离线线程表
DROP TABLE IF EXISTS batch_offline_thread CASCADE;

CREATE TABLE batch_offline_thread (
    id SERIAL PRIMARY KEY,
    task_id BIGINT,
    batch_id BIGINT,
    service_id INTEGER DEFAULT 0,
    thread_status INTEGER DEFAULT 1
);

COMMENT ON TABLE batch_offline_thread IS '批次离线线程表';
COMMENT ON COLUMN batch_offline_thread.task_id IS '任务ID';
COMMENT ON COLUMN batch_offline_thread.batch_id IS '批次ID';
COMMENT ON COLUMN batch_offline_thread.service_id IS '服务ID';
COMMENT ON COLUMN batch_offline_thread.thread_status IS '线程状态';

-- ========================================
-- 数据导出任务管理表
-- ========================================

-- 数据导出任务表
DROP TABLE IF EXISTS data_export_task CASCADE;

CREATE TABLE data_export_task (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT,
    show_query VARCHAR(2048),
    type INTEGER NOT NULL,
    session_id TEXT,
    state INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status INTEGER NOT NULL DEFAULT 1,
    task_id VARCHAR(200)
);

COMMENT ON TABLE data_export_task IS '数据导出任务表';
COMMENT ON COLUMN data_export_task.created_by IS '创建者用户ID';
COMMENT ON COLUMN data_export_task.path IS '文件路径';
COMMENT ON COLUMN data_export_task.query IS 'ES下载检索条件';
COMMENT ON COLUMN data_export_task.show_query IS '前端展示条件';
COMMENT ON COLUMN data_export_task.type IS '全量下载为1，部分下载为0';
COMMENT ON COLUMN data_export_task.session_id IS 'session列表信息';
COMMENT ON COLUMN data_export_task.state IS '0准备数据 1可下载 2重新下载 3已删除 4待删除';
COMMENT ON COLUMN data_export_task.created_at IS '创建时间';
COMMENT ON COLUMN data_export_task.end_time IS '结束时间';
COMMENT ON COLUMN data_export_task.status IS '数据状态 0删除 1存在';
COMMENT ON COLUMN data_export_task.task_id IS '任务ID（数组）';

-- 数据导出任务注册表
DROP TABLE IF EXISTS data_export_task_register CASCADE;

CREATE TABLE data_export_task_register (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT NOT NULL,
    type INTEGER NOT NULL,
    download_count INTEGER NOT NULL DEFAULT 0,
    delete_time TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type INTEGER NOT NULL DEFAULT 1,
    status INTEGER NOT NULL DEFAULT 1,
    error_msg VARCHAR(255)
);

COMMENT ON TABLE data_export_task_register IS '数据导出任务注册表';
COMMENT ON COLUMN data_export_task_register.created_by IS '创建者用户ID';
COMMENT ON COLUMN data_export_task_register.path IS '日志保存路径';
COMMENT ON COLUMN data_export_task_register.query IS '查询条件';
COMMENT ON COLUMN data_export_task_register.type IS '日志状态，1待执行，2准备数据，3待下载，4已删除，-1错误';
COMMENT ON COLUMN data_export_task_register.download_count IS '下载次数';
COMMENT ON COLUMN data_export_task_register.delete_time IS '删除时间';
COMMENT ON COLUMN data_export_task_register.updated_at IS '更新时间';
COMMENT ON COLUMN data_export_task_register.created_at IS '创建时间';
COMMENT ON COLUMN data_export_task_register.task_type IS '1会话分析、2会话聚合、3元数据_SSL、4元数据_HTTP、5元数据_DNS';
COMMENT ON COLUMN data_export_task_register.status IS '0已删除 1存在';
COMMENT ON COLUMN data_export_task_register.error_msg IS '任务失败的原因';

-- ========================================
-- PCAP下载任务管理表
-- ========================================

-- PCAP下载任务表
DROP TABLE IF EXISTS pcap_download_task CASCADE;

CREATE TABLE pcap_download_task (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    user_id VARCHAR(64) NOT NULL,
    alarm_type VARCHAR(100),
    alarm_time BIGINT,
    session_ids TEXT NOT NULL,
    pcap_file_paths TEXT,
    archive_file_path VARCHAR(500),
    archive_file_size BIGINT DEFAULT 0,
    total_file_count INTEGER DEFAULT 0,
    processed_file_count INTEGER DEFAULT 0,
    status SMALLINT DEFAULT 1,
    progress SMALLINT DEFAULT 0,
    error_message TEXT,
    download_url VARCHAR(500),
    expire_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    start_time TIMESTAMP,
    complete_time TIMESTAMP
);

COMMENT ON TABLE pcap_download_task IS 'PCAP下载任务表';
COMMENT ON COLUMN pcap_download_task.id IS '主键';
COMMENT ON COLUMN pcap_download_task.task_id IS '任务ID（关联task_analysis表）';
COMMENT ON COLUMN pcap_download_task.user_id IS '创建者用户ID';
COMMENT ON COLUMN pcap_download_task.alarm_type IS '告警类型';
COMMENT ON COLUMN pcap_download_task.alarm_time IS '告警时间（时间戳）';
COMMENT ON COLUMN pcap_download_task.session_ids IS '会话ID列表（JSON格式）';
COMMENT ON COLUMN pcap_download_task.pcap_file_paths IS 'PCAP文件路径列表（JSON格式）';
COMMENT ON COLUMN pcap_download_task.archive_file_path IS '合并后的PCAP文件路径';
COMMENT ON COLUMN pcap_download_task.archive_file_size IS '合并后文件大小（字节）';
COMMENT ON COLUMN pcap_download_task.total_file_count IS '总文件数量';
COMMENT ON COLUMN pcap_download_task.processed_file_count IS '已处理文件数量';
COMMENT ON COLUMN pcap_download_task.status IS '任务状态：1-待处理，2-处理中，3-已完成，-1-失败';
COMMENT ON COLUMN pcap_download_task.progress IS '进度百分比（0-100）';
COMMENT ON COLUMN pcap_download_task.error_message IS '错误信息';
COMMENT ON COLUMN pcap_download_task.download_url IS '下载URL';
COMMENT ON COLUMN pcap_download_task.expire_time IS '文件过期时间';
COMMENT ON COLUMN pcap_download_task.create_time IS '创建时间';
COMMENT ON COLUMN pcap_download_task.update_time IS '更新时间';
COMMENT ON COLUMN pcap_download_task.start_time IS '开始处理时间';
COMMENT ON COLUMN pcap_download_task.complete_time IS '完成时间';


-- ========================================
-- 创建索引
-- ========================================

-- 任务分析表索引
CREATE INDEX idx_task_analysis_task_id ON task_analysis (task_id);
CREATE INDEX idx_task_analysis_created_by ON task_analysis (created_by);
CREATE INDEX idx_task_analysis_task_type ON task_analysis (task_type);
CREATE INDEX idx_task_analysis_task_status ON task_analysis (task_status);
CREATE INDEX idx_task_analysis_created_at ON task_analysis (created_at);

-- 任务批次表索引
CREATE INDEX idx_task_batch_task_id ON task_batch (task_id);
CREATE INDEX idx_task_batch_batch_status ON task_batch (batch_status);
CREATE INDEX idx_task_batch_created_at ON task_batch (created_at);

-- 任务批次插件关联表索引
CREATE INDEX idx_task_batch_plugin_batch_id ON task_batch_plugin (batch_id);
CREATE INDEX idx_task_batch_plugin_plugin_id ON task_batch_plugin (plugin_id);

-- 任务内部IP配置表索引
CREATE INDEX idx_task_internal_ip_task_id ON task_internal_ip (task_id);

-- 离线任务批次文件表索引
CREATE INDEX idx_offline_task_batch_file_task_id ON offline_task_batch_file (task_id);
CREATE INDEX idx_offline_task_batch_file_batch_id ON offline_task_batch_file (batch_id);

-- 批次离线线程表索引
CREATE INDEX idx_batch_offline_thread_task_id ON batch_offline_thread (task_id);
CREATE INDEX idx_batch_offline_thread_batch_id ON batch_offline_thread (batch_id);

-- 数据导出任务表索引
CREATE INDEX idx_data_export_task_created_by ON data_export_task (created_by);
CREATE INDEX idx_data_export_task_state ON data_export_task (state);
CREATE INDEX idx_data_export_task_status ON data_export_task (status);
CREATE INDEX idx_data_export_task_created_at ON data_export_task (created_at);

-- 数据导出任务注册表索引
CREATE INDEX idx_data_export_task_register_created_by ON data_export_task_register (created_by);
CREATE INDEX idx_data_export_task_register_type ON data_export_task_register (type);
CREATE INDEX idx_data_export_task_register_task_type ON data_export_task_register (task_type);
CREATE INDEX idx_data_export_task_register_status ON data_export_task_register (status);

-- PCAP下载任务表索引
CREATE INDEX idx_pcap_download_task_user_id ON pcap_download_task (user_id);
CREATE INDEX idx_pcap_download_task_status ON pcap_download_task (status);
CREATE INDEX idx_pcap_download_task_create_time ON pcap_download_task (create_time);
CREATE INDEX idx_pcap_download_task_expire_time ON pcap_download_task (expire_time);
CREATE INDEX idx_pcap_download_task_task_id ON pcap_download_task (task_id);
CREATE INDEX idx_pcap_download_task_alarm_type ON pcap_download_task (alarm_type);
CREATE INDEX idx_pcap_download_task_alarm_time ON pcap_download_task (alarm_time);



-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为相关表创建更新时间触发器
CREATE TRIGGER update_task_analysis_updated_at 
    BEFORE UPDATE ON task_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_batch_updated_at 
    BEFORE UPDATE ON task_batch 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_internal_ip_updated_at 
    BEFORE UPDATE ON task_internal_ip 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_export_task_register_updated_at
    BEFORE UPDATE ON data_export_task_register
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pcap_download_task_updated_at
    BEFORE UPDATE ON pcap_download_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
